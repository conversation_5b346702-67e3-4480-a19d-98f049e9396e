# HeroModern Theme - API 集成文档

## 🎯 概述

HeroModern 主题完全基于 [v2board API 文档](https://github.com/cdnf/v2board-api-document) 实现，提供了完整的前端功能。

## 🔗 API 端点映射

### 认证相关 (Passport)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 获取配置 | GET | `/api/v1/passport/comm/config` | ✅ |
| 登录校验 | GET | `/api/v1/passport/auth/check` | ✅ |
| 登录账号 | POST | `/api/v1/passport/auth/login` | ✅ |
| 发送邮箱验证码 | POST | `/api/v1/passport/comm/sendEmailVerify` | ✅ |
| 注册账号 | POST | `/api/v1/passport/auth/register` | ✅ |
| 重置密码 | POST | `/api/v1/passport/auth/forget` | ✅ |

### 用户相关 (User)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 登出 | GET | `/api/v1/user/logout` | ✅ |
| 获取用户信息 | GET | `/api/v1/user/info` | ✅ |
| 获取订阅信息 | GET | `/api/v1/user/getSubscribe` | ✅ |
| 重置订阅链接 | GET | `/api/v1/user/resetSecurity` | ✅ |
| 获取代办事项 | GET | `/api/v1/user/getStat` | ✅ |
| 修改密码 | POST | `/api/v1/user/changePassword` | ✅ |
| 更新通知状态 | POST | `/api/v1/user/update` | ✅ |
| 佣金划转 | POST | `/api/v1/user/transfer` | ✅ |

### 套餐相关 (Plan)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 获取套餐列表 | GET | `/api/v1/user/plan/fetch` | ✅ |

### 订单相关 (Order)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 获取订单列表 | GET | `/api/v1/user/order/fetch` | ✅ |
| 获取支付方式 | GET | `/api/v1/user/order/getPaymentMethod` | ✅ |
| 获取订单详情 | GET | `/api/v1/user/order/details` | ✅ |
| 检查订单状态 | GET | `/api/v1/user/order/check` | ✅ |
| 创建订单 | POST | `/api/v1/user/order/save` | ✅ |
| 结算订单 | POST | `/api/v1/user/order/checkout` | ✅ |
| 取消订单 | POST | `/api/v1/user/order/cancel` | ✅ |

### 服务器相关 (Server)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 获取服务器列表 | GET | `/api/v1/user/server/fetch` | ✅ |
| 获取流量记录 | GET | `/api/v1/user/server/log/fetch` | ✅ |

### 邀请相关 (Invite)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 获取邀请信息 | GET | `/api/v1/user/invite/fetch` | ✅ |
| 保存邀请码 | POST | `/api/v1/user/invite/save` | ✅ |

### 工单相关 (Ticket)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 获取工单列表 | GET | `/api/v1/user/ticket/fetch` | ✅ |
| 创建工单 | POST | `/api/v1/user/ticket/save` | ✅ |
| 回复工单 | POST | `/api/v1/user/ticket/reply` | ✅ |
| 关闭工单 | POST | `/api/v1/user/ticket/close` | ✅ |

### 知识库相关 (Knowledge)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 获取知识库文章 | GET | `/api/v1/user/knowledge/fetch` | ✅ |

### 公告相关 (Notice)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 获取公告 | GET | `/api/v1/user/notice/fetch` | ✅ |

### 优惠券相关 (Coupon)

| 功能 | 方法 | 端点 | 实现状态 |
|------|------|------|----------|
| 检查优惠券 | POST | `/api/v1/user/coupon/check` | ✅ |

## 🛠️ 技术实现

### API 服务层 (`hero-api.js`)

```javascript
// 示例：登录API调用
const result = await window.HeroAPI.login({
  email: '<EMAIL>',
  password: 'password'
});

if (result.success) {
  // 登录成功
  console.log('User data:', result.data);
} else {
  // 登录失败
  console.error('Login failed:', result.message);
}
```

### 认证管理 (`hero-auth.js`)

- **自动token管理**: 自动存储和使用认证token
- **过期处理**: 自动检测token过期并重新登录
- **状态同步**: 全局认证状态管理

### 错误处理

```javascript
// 统一的错误处理
try {
  const result = await window.HeroAPI.getUser();
  if (result.success) {
    // 处理成功响应
  } else {
    // 处理业务错误
    hero.notify(result.message, 'error');
  }
} catch (error) {
  // 处理网络错误
  hero.notify('网络错误，请稍后重试', 'error');
}
```

## 🔧 配置选项

### API 配置

```javascript
window.settings = {
  api: {
    base_url: '/api/v1',
    timeout: 30000
  }
};
```

### 认证配置

- **Token存储**: localStorage (持久) 或 sessionStorage (会话)
- **自动重试**: 网络错误时自动重试
- **超时设置**: 可配置的请求超时时间

## 📊 数据格式

### 标准响应格式

```json
{
  "success": true,
  "data": { ... },
  "message": "操作成功"
}
```

### 错误响应格式

```json
{
  "success": false,
  "data": null,
  "message": "错误信息"
}
```

## 🧪 测试工具

### API 测试页面 (`api-test.html`)

提供完整的API测试界面：

1. **认证测试**: 登录、注册、忘记密码
2. **用户测试**: 用户信息、订阅信息、统计数据
3. **订单测试**: 套餐列表、订单管理、支付流程
4. **功能测试**: 邀请、工单、知识库等

### 使用方法

```bash
# 在浏览器中打开
open api-test.html

# 或者通过HTTP服务器访问
python -m http.server 8000
# 然后访问 http://localhost:8000/api-test.html
```

## 🔄 降级策略

当API不可用时，主题会自动使用模拟数据：

```javascript
// 示例：带降级的API调用
const result = await api.getPlans();
if (result.success && result.data) {
  setPlans(result.data);
} else {
  // 使用模拟数据作为后备
  setPlans(mockPlansData);
}
```

## 📈 性能优化

- **请求缓存**: 避免重复请求相同数据
- **并发控制**: 合理控制并发请求数量
- **超时处理**: 防止长时间等待
- **错误重试**: 智能重试机制

## 🔒 安全特性

- **CSRF保护**: 自动添加CSRF token
- **XSS防护**: 输入输出过滤
- **认证验证**: 每次请求验证token有效性
- **权限控制**: 基于用户角色的访问控制

---

**HeroModern 主题提供了完整的 v2board API 集成，确保与后端系统的完美兼容性。**
