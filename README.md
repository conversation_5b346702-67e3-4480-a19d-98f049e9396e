# HeroModern Theme

基于 HeroUI 的现代化 v2board 主题，提供简洁美观的设计和流畅的交互体验。

**完整功能实现** - 参考 CyberPunk 主题的 API 实现，提供完整的前端应用功能。

## 特性

### 🚀 完整功能实现
- **完整的 React 应用** - 基于 React 18 的现代化单页应用
- **完整的 API 集成** - 参考 CyberPunk 主题实现所有 v2board API
- **实时数据更新** - 真实的数据获取和状态管理
- **完整的页面功能** - 仪表板、订阅、购买、流量、订单、邀请、工单、知识库、个人资料

### 🎨 现代化设计
- 基于 HeroUI 组件库的现代化界面设计
- 简洁优雅的视觉风格
- 流畅的动画效果和交互体验

### 🌈 多主题色彩
- 现代蓝 (默认)
- 优雅紫
- 自然绿
- 活力橙
- 温馨粉
- 商务灰

### 🌓 智能主题模式
- 亮色模式
- 暗色模式
- 跟随系统 (默认)

### 📱 响应式设计
- 完美适配桌面端、平板和移动设备
- 自适应布局和组件大小
- 移动端优化的交互体验

### ⚡ 性能优化
- 轻量级组件库
- 优化的CSS和JavaScript
- 快速加载和流畅动画

### 🎛️ 高度可定制
- 多种边栏风格 (现代、极简、经典)
- 多种导航栏风格 (浮动、固定、透明)
- 可调节的圆角风格
- 可选的毛玻璃效果
- 自定义背景图片支持

## 配置选项

### 主题色
选择您喜欢的主题颜色：
- `blue` - 现代蓝 (#006FEE)
- `purple` - 优雅紫 (#7C3AED)
- `green` - 自然绿 (#17C964)
- `orange` - 活力橙 (#F5A524)
- `pink` - 温馨粉 (#F31260)
- `slate` - 商务灰 (#64748B)

### 界面模式
- `light` - 亮色模式
- `dark` - 暗色模式
- `auto` - 跟随系统 (推荐)

### 边栏风格
- `modern` - 现代风格 (默认)
- `minimal` - 极简风格
- `classic` - 经典风格

### 导航栏风格
- `floating` - 浮动式 (默认)
- `sticky` - 固定式
- `transparent` - 透明式

### 圆角风格
- `small` - 小圆角
- `medium` - 中圆角 (默认)
- `large` - 大圆角
- `none` - 无圆角

### 特效选项
- `enable_animations` - 启用/禁用动画效果
- `enable_blur` - 启用/禁用毛玻璃效果
- `background_url` - 自定义背景图片URL

## 技术特性

### 组件库
- 基于 HeroUI 设计系统
- 现代化的 React 组件
- 完整的无障碍支持
- TypeScript 类型支持

### 样式系统
- CSS 变量驱动的主题系统
- 响应式设计原则
- 优化的动画性能
- 现代 CSS 特性支持

### 国际化
支持多种语言：
- 简体中文 (zh-CN)
- 英语 (en-US)
- 日语 (ja-JP)
- 越南语 (vi-VN)
- 韩语 (ko-KR)
- 繁体中文 (zh-TW)
- 波斯语 (fa-IR)

## 浏览器支持

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 安装使用

### 1. 安装主题

#### 自动安装（推荐）
```bash
php theme/HeroModern/install.php install
```

#### 手动安装
1. 将主题文件复制到 v2board 的 `theme/HeroModern/` 目录
2. 在管理后台的主题设置中选择 "HeroModern"
3. 根据需要调整主题配置选项
4. 保存设置并刷新页面

### 2. 配置主题

在 v2board 管理后台的主题设置中，可以配置以下选项：

- **主题色彩**: 选择 6 种预设颜色之一
- **界面模式**: 亮色/暗色/跟随系统
- **布局风格**: 侧边栏和导航栏样式
- **视觉效果**: 动画、毛玻璃效果、圆角风格
- **自定义背景**: 设置背景图片URL

### 3. 测试功能

#### 完整功能测试
- 打开 `test.html` 查看完整的React应用功能
- 包含所有页面和API集成

#### 组件演示
- 打开 `demo.html` 查看HeroUI组件演示
- 可以实时切换主题色彩和模式

#### API测试
- 打开 `api-test.html` 测试所有v2board API接口
- 验证API连接和数据格式

### 4. UMI 架构兼容

主题完全兼容 v2board 的 UMI 框架：
- **前端路由**: 基于 UMI 的单页应用架构
- **自动认证**: 自动检测登录状态并路由到相应页面
- **组件化**: 模块化的组件设计，易于扩展
- **API集成**: 完整的 v2board API 集成

## API 功能

### 完整的 v2board API 集成
- **用户管理** - 登录、注册、个人资料、密码修改、两步验证
- **订阅管理** - 获取订阅信息、套餐列表、购买订阅
- **订单管理** - 订单列表、订单详情、订单取消
- **流量统计** - 流量记录、使用统计、服务器日志
- **邀请系统** - 邀请码生成、邀请记录、佣金统计
- **工单系统** - 工单列表、创建工单、回复工单
- **知识库** - 文章列表、文章详情、搜索功能
- **支付系统** - 支付方式、订单结算、支付回调
- **配置管理** - 客户端配置、订阅链接、安全重置

### 完整的 v2board API 支持

根据 [v2board API 文档](https://github.com/cdnf/v2board-api-document) 实现：

#### Passport 相关
- ✅ `GET /api/v1/passport/comm/config` - 获取配置
- ✅ `GET /api/v1/passport/auth/check` - 登录校验
- ✅ `POST /api/v1/passport/auth/login` - 登录账号
- ✅ `POST /api/v1/passport/comm/sendEmailVerify` - 发送邮箱验证码
- ✅ `POST /api/v1/passport/auth/register` - 注册账号
- ✅ `POST /api/v1/passport/auth/forget` - 重置密码

#### User 相关
- ✅ `GET /api/v1/user/logout` - 登出
- ✅ `GET /api/v1/user/info` - 获取用户信息
- ✅ `GET /api/v1/user/getSubscribe` - 获取订阅信息
- ✅ `GET /api/v1/user/resetSecurity` - 重置订阅链接
- ✅ `GET /api/v1/user/getStat` - 获取代办事项
- ✅ `POST /api/v1/user/changePassword` - 修改密码
- ✅ `POST /api/v1/user/update` - 更新通知状态
- ✅ `POST /api/v1/user/transfer` - 佣金划转

#### Plan & Order 相关
- ✅ `GET /api/v1/user/plan/fetch` - 获取套餐列表
- ✅ `GET /api/v1/user/order/fetch` - 获取订单列表
- ✅ `GET /api/v1/user/order/getPaymentMethod` - 获取支付方式
- ✅ `GET /api/v1/user/order/details` - 获取订单详情
- ✅ `GET /api/v1/user/order/check` - 检查订单状态
- ✅ `POST /api/v1/user/order/save` - 创建订单
- ✅ `POST /api/v1/user/order/checkout` - 结算订单
- ✅ `POST /api/v1/user/order/cancel` - 取消订单

#### Server & Traffic 相关
- ✅ `GET /api/v1/user/server/fetch` - 获取服务器列表
- ✅ `GET /api/v1/user/server/log/fetch` - 获取流量记录

#### Invite 相关
- ✅ `GET /api/v1/user/invite/fetch` - 获取邀请信息
- ✅ `POST /api/v1/user/invite/save` - 保存邀请码

#### Ticket 相关
- ✅ `GET /api/v1/user/ticket/fetch` - 获取工单列表
- ✅ `POST /api/v1/user/ticket/save` - 创建工单
- ✅ `POST /api/v1/user/ticket/reply` - 回复工单
- ✅ `POST /api/v1/user/ticket/close` - 关闭工单

#### Knowledge & Notice 相关
- ✅ `GET /api/v1/user/knowledge/fetch` - 获取知识库文章
- ✅ `GET /api/v1/user/notice/fetch` - 获取公告

#### Coupon 相关
- ✅ `POST /api/v1/user/coupon/check` - 检查优惠券

### API 服务特性
- **自动认证**: 自动添加Bearer token到请求头
- **错误处理**: 统一的错误处理和用户提示
- **超时控制**: 可配置的请求超时时间
- **重试机制**: 网络错误自动重试
- **CSRF保护**: 自动添加CSRF token
- **降级方案**: API失败时使用模拟数据

## 自定义开发

### 文件结构
```
theme/HeroModern/
├── config.json              # 主题配置文件
├── dashboard.blade.php      # UMI 主模板文件
├── README.md               # 说明文档
├── FEATURES.md             # 功能列表
├── API-INTEGRATION.md      # API集成文档
├── CHANGELOG.md            # 更新日志
├── LICENSE                 # MIT许可证
├── install.php             # 自动安装脚本
├── demo.html              # 组件演示页面
├── test.html              # 完整功能测试页面
├── api-test.html          # API接口测试页面
└── assets/                # 资源文件
    ├── components.chunk.css # UMI 组件样式
    ├── umi.css            # UMI 主样式文件
    ├── umi.js             # UMI 主应用文件
    ├── vendors.async.js   # 第三方库
    ├── components.async.js # 异步组件
    ├── heroui.min.css     # HeroUI 样式文件
    ├── heroui.min.js      # HeroUI 组件库
    ├── modern-theme.css   # 主题样式
    ├── modern-theme.js    # 主题脚本
    ├── hero-api.js        # API 服务层
    ├── hero-auth.js       # 认证组件
    ├── hero-app.js        # React 主应用
    ├── hero-pages.js      # 页面组件
    └── i18n/             # 国际化文件
        ├── zh-CN.js      # 简体中文
        ├── zh-TW.js      # 繁体中文
        ├── en-US.js      # 英语
        ├── ja-JP.js      # 日语
        └── ko-KR.js      # 韩语
```

### CSS 变量
主题使用 CSS 变量系统，您可以通过修改变量来自定义样式：

```css
:root {
  --heroui-primary: #006FEE;
  --heroui-secondary: #9353D3;
  --heroui-success: #17C964;
  --heroui-warning: #F5A524;
  --heroui-danger: #F31260;
  /* 更多变量... */
}
```

### JavaScript API
主题提供了丰富的 JavaScript API：

```javascript
// 主题管理
window.hero.theme.setTheme('dark');

// 工具函数
window.hero.utils.formatDate(new Date());

// 通知系统
window.hero.notify('操作成功', 'success');
```

## 更新日志

### v1.0.0
- 初始版本发布
- 基于 HeroUI 的现代化设计
- 支持多主题色彩和模式
- 响应式布局和动画效果
- 完整的国际化支持

## 许可证

MIT License

## 支持

如果您在使用过程中遇到问题或有建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 在线客服

---

感谢您选择 HeroModern 主题！
