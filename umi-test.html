<!DOCTYPE html>
<html>

<head>
  <!-- HeroModern Theme CSS -->
  <link rel="stylesheet" href="assets/components.chunk.css">
  <link rel="stylesheet" href="assets/umi.css">
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
  <meta name="theme-color" content="#006FEE">

  <title>HeroModern UMI Test</title>
  <script>window.routerBase = "/";</script>
  <script>
    window.settings = {
      title: 'HeroModern UMI Test',
      assets_path: 'assets',
      theme: {
        mode: 'auto',
        color: 'blue',
        sidebar: 'modern',
        header: 'floating',
        animations: true,
        blur: true,
        border_radius: 'medium'
      },
      version: '1.0.0',
      background_url: '',
      description: 'HeroModern UMI 架构测试',
      i18n: [
        'zh-CN',
        'zh-TW',
        'en-US', 
        'ja-JP',
        'ko-KR'
      ],
      logo: ''
    }
  </script>
  
  <!-- 国际化文件 -->
  <script src="assets/i18n/zh-CN.js"></script>
  <script src="assets/i18n/zh-TW.js"></script>
  <script src="assets/i18n/en-US.js"></script>
  <script src="assets/i18n/ja-JP.js"></script>
  <script src="assets/i18n/ko-KR.js"></script>

  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: var(--heroui-background, #ffffff);
      color: var(--heroui-foreground, #11181c);
    }

    .test-header {
      background: var(--heroui-content1, #ffffff);
      border-bottom: 1px solid var(--heroui-divider, #e4e4e7);
      padding: 20px;
      text-align: center;
    }

    .test-status {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin: 20px;
      flex-wrap: wrap;
    }

    .status-item {
      background: var(--heroui-content1, #ffffff);
      border: 1px solid var(--heroui-divider, #e4e4e7);
      border-radius: 8px;
      padding: 16px;
      min-width: 200px;
      text-align: center;
    }

    .status-ok {
      border-color: var(--heroui-success, #17c964);
      background: rgba(23, 201, 100, 0.1);
    }

    .status-error {
      border-color: var(--heroui-danger, #f31260);
      background: rgba(243, 18, 96, 0.1);
    }

    .test-controls {
      text-align: center;
      margin: 20px;
    }

    .test-button {
      background: var(--heroui-primary, #006fee);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 8px 16px;
      margin: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    .test-button:hover {
      opacity: 0.8;
    }

    .test-log {
      background: var(--heroui-content1, #ffffff);
      border: 1px solid var(--heroui-divider, #e4e4e7);
      border-radius: 8px;
      margin: 20px;
      padding: 16px;
      max-height: 400px;
      overflow-y: auto;
      font-family: monospace;
      font-size: 12px;
    }

    .log-entry {
      margin: 4px 0;
      padding: 4px 8px;
      border-radius: 4px;
    }

    .log-info {
      background: rgba(0, 111, 238, 0.1);
      color: var(--heroui-primary, #006fee);
    }

    .log-success {
      background: rgba(23, 201, 100, 0.1);
      color: var(--heroui-success, #17c964);
    }

    .log-error {
      background: rgba(243, 18, 96, 0.1);
      color: var(--heroui-danger, #f31260);
    }
  </style>
</head>

<body>
  <div class="test-header">
    <h1>🚀 HeroModern UMI 架构测试</h1>
    <p>测试 UMI 框架兼容性和组件加载</p>
  </div>

  <div class="test-status" id="status-container">
    <div class="status-item" id="react-status">
      <h3>React</h3>
      <div id="react-text">检查中...</div>
    </div>
    <div class="status-item" id="umi-status">
      <h3>UMI</h3>
      <div id="umi-text">检查中...</div>
    </div>
    <div class="status-item" id="api-status">
      <h3>API</h3>
      <div id="api-text">检查中...</div>
    </div>
    <div class="status-item" id="components-status">
      <h3>组件</h3>
      <div id="components-text">检查中...</div>
    </div>
  </div>

  <div class="test-controls">
    <button class="test-button" onclick="testThemeSwitch()">切换主题</button>
    <button class="test-button" onclick="testColorSwitch()">切换颜色</button>
    <button class="test-button" onclick="testNotification()">测试通知</button>
    <button class="test-button" onclick="testAPI()">测试API</button>
    <button class="test-button" onclick="clearLog()">清空日志</button>
  </div>

  <div id="root"></div>

  <div class="test-log" id="test-log">
    <div class="log-entry log-info">🚀 开始加载 HeroModern UMI 测试...</div>
  </div>

  <!-- React CDN -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

  <!-- UMI 脚本 -->
  <script src="assets/vendors.async.js"></script>
  <script src="assets/components.async.js"></script>
  <script src="assets/hero-api.js"></script>
  <script src="assets/hero-auth.js"></script>
  <script src="assets/hero-pages.js"></script>
  <script src="assets/umi.js"></script>

  <script>
    // 测试日志
    function log(message, type = 'info') {
      const logContainer = document.getElementById('test-log');
      const entry = document.createElement('div');
      entry.className = `log-entry log-${type}`;
      entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logContainer.appendChild(entry);
      logContainer.scrollTop = logContainer.scrollHeight;
    }

    // 更新状态
    function updateStatus(id, text, isOk) {
      const element = document.getElementById(id);
      const textElement = document.getElementById(id.replace('-status', '-text'));
      
      element.className = `status-item ${isOk ? 'status-ok' : 'status-error'}`;
      textElement.textContent = text;
    }

    // 检查依赖
    function checkDependencies() {
      log('检查依赖项...', 'info');

      // 检查 React
      if (typeof React !== 'undefined' && typeof ReactDOM !== 'undefined') {
        updateStatus('react-status', '✅ 已加载', true);
        log('React 加载成功', 'success');
      } else {
        updateStatus('react-status', '❌ 未加载', false);
        log('React 加载失败', 'error');
      }

      // 检查 UMI 相关
      setTimeout(() => {
        if (window.settings && window.HeroUtils) {
          updateStatus('umi-status', '✅ 已加载', true);
          log('UMI 工具加载成功', 'success');
        } else {
          updateStatus('umi-status', '❌ 未加载', false);
          log('UMI 工具加载失败', 'error');
        }
      }, 1000);

      // 检查 API
      setTimeout(() => {
        if (window.HeroAPI) {
          updateStatus('api-status', '✅ 已加载', true);
          log('API 服务加载成功', 'success');
        } else {
          updateStatus('api-status', '❌ 未加载', false);
          log('API 服务加载失败', 'error');
        }
      }, 1500);

      // 检查组件
      setTimeout(() => {
        if (window.HeroComponents && window.Dashboard) {
          updateStatus('components-status', '✅ 已加载', true);
          log('页面组件加载成功', 'success');
        } else {
          updateStatus('components-status', '❌ 未加载', false);
          log('页面组件加载失败', 'error');
        }
      }, 2000);
    }

    // 测试函数
    function testThemeSwitch() {
      if (window.HeroUtils && window.HeroUtils.toggleTheme) {
        const newTheme = window.HeroUtils.toggleTheme();
        log(`主题切换到: ${newTheme}`, 'success');
      } else {
        log('主题切换功能不可用', 'error');
      }
    }

    function testColorSwitch() {
      if (window.HeroUtils && window.HeroUtils.applyThemeColor) {
        const colors = ['blue', 'purple', 'green', 'orange', 'pink', 'slate'];
        const randomColor = colors[Math.floor(Math.random() * colors.length)];
        window.HeroUtils.applyThemeColor(randomColor);
        log(`颜色切换到: ${randomColor}`, 'success');
      } else {
        log('颜色切换功能不可用', 'error');
      }
    }

    function testNotification() {
      if (window.HeroUtils && window.HeroUtils.notify) {
        window.HeroUtils.notify('这是一个测试通知', 'success');
        log('通知测试成功', 'success');
      } else {
        log('通知功能不可用', 'error');
      }
    }

    async function testAPI() {
      if (window.HeroAPI) {
        try {
          log('测试 API 配置获取...', 'info');
          const result = await window.HeroAPI.getConfig();
          log(`API 测试成功: ${JSON.stringify(result)}`, 'success');
        } catch (error) {
          log(`API 测试失败: ${error.message}`, 'error');
        }
      } else {
        log('API 服务不可用', 'error');
      }
    }

    function clearLog() {
      const logContainer = document.getElementById('test-log');
      logContainer.innerHTML = '<div class="log-entry log-info">🚀 日志已清空</div>';
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
      log('DOM 加载完成', 'success');
      checkDependencies();
    });

    // 监听组件加载完成
    window.addEventListener('heroComponentsReady', () => {
      log('HeroComponents 组件库加载完成', 'success');
    });

    log('测试脚本加载完成', 'success');
  </script>
</body>

</html>
