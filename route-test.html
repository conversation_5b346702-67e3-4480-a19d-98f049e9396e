<!DOCTYPE html>
<html>

<head>
  <!-- HeroModern Theme CSS -->
  <link rel="stylesheet" href="assets/components.chunk.css">
  <link rel="stylesheet" href="assets/umi.css">
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
  <meta name="theme-color" content="#006FEE">

  <title>HeroModern Route Test</title>
  <script>window.routerBase = "/";</script>
  <script>
    window.settings = {
      title: 'HeroModern Route Test',
      assets_path: 'assets',
      theme: {
        mode: 'auto',
        color: 'blue',
        sidebar: 'modern',
        header: 'floating',
        animations: true,
        blur: true,
        border_radius: 'medium'
      },
      version: '1.0.0',
      background_url: '',
      description: 'HeroModern 路由测试',
      i18n: [
        'zh-CN',
        'zh-TW',
        'en-US', 
        'ja-<PERSON>',
        'ko-KR'
      ],
      logo: ''
    }
  </script>

  <style>
    .test-controls {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      border: 1px solid #e4e4e7;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      max-width: 300px;
    }

    .test-button {
      background: #006fee;
      color: white;
      border: none;
      border-radius: 6px;
      padding: 6px 12px;
      margin: 4px;
      cursor: pointer;
      font-size: 12px;
      display: block;
      width: 100%;
    }

    .test-button:hover {
      background: #0056b3;
    }

    .test-status {
      font-size: 12px;
      margin: 8px 0;
      padding: 8px;
      border-radius: 4px;
      background: #f8f9fa;
    }

    .status-success {
      background: #d4edda;
      color: #155724;
    }

    .status-error {
      background: #f8d7da;
      color: #721c24;
    }

    .status-info {
      background: #d1ecf1;
      color: #0c5460;
    }
  </style>
</head>

<body>
  <div class="test-controls">
    <h3 style="margin: 0 0 12px 0; font-size: 14px;">🧪 路由测试</h3>
    
    <div id="auth-status" class="test-status">检查认证状态...</div>
    
    <button class="test-button" onclick="testLogin()">模拟登录</button>
    <button class="test-button" onclick="testLogout()">模拟登出</button>
    <button class="test-button" onclick="testAuthCheck()">检查认证</button>
    <button class="test-button" onclick="testRouteChange()">测试路由</button>
    <button class="test-button" onclick="clearStatus()">清空状态</button>
    
    <div id="test-log" style="font-size: 11px; margin-top: 12px; max-height: 200px; overflow-y: auto;"></div>
  </div>

  <div id="root">
    <!-- 加载指示器 -->
    <div style="
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background: #ffffff;
      color: #11181c;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    ">
      <div style="text-align: center;">
        <div style="
          width: 40px;
          height: 40px;
          border: 4px solid #e4e4e7;
          border-top: 4px solid #006fee;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px auto;
        "></div>
        <p style="margin: 0; color: #71717a;">正在加载 HeroModern 路由测试...</p>
      </div>
    </div>
    
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </div>
  
  <!-- React 依赖 -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  
  <!-- 国际化文件 -->
  <script src="assets/i18n/zh-CN.js"></script>
  <script src="assets/i18n/zh-TW.js"></script>
  <script src="assets/i18n/en-US.js"></script>
  <script src="assets/i18n/ja-JP.js"></script>
  <script src="assets/i18n/ko-KR.js"></script>
  
  <!-- 等待React加载完成 -->
  <script>
    function waitForReact(callback) {
      if (typeof React !== 'undefined' && typeof ReactDOM !== 'undefined') {
        console.log('✅ React loaded successfully');
        callback();
      } else {
        console.log('⏳ Waiting for React...');
        setTimeout(() => waitForReact(callback), 100);
      }
    }
    
    // 加载应用脚本
    function loadAppScripts() {
      console.log('📦 Loading application scripts...');
      
      const scripts = [
        'assets/vendors.async.js',
        'assets/components.async.js',
        'assets/hero-api.js',
        'assets/hero-auth.js',
        'assets/hero-pages.js',
        'assets/umi.js'
      ];
      
      let loadedCount = 0;
      
      scripts.forEach((src, index) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => {
          loadedCount++;
          log(`✅ Loaded (${loadedCount}/${scripts.length}): ${src.split('/').pop()}`);
          if (loadedCount === scripts.length) {
            log('🎉 All scripts loaded successfully');
            setupEventListeners();
          }
        };
        script.onerror = () => {
          log(`❌ Failed to load: ${src.split('/').pop()}`);
        };
        document.head.appendChild(script);
      });
    }

    // 设置事件监听器
    function setupEventListeners() {
      // 监听认证事件
      window.addEventListener('auth:showLogin', () => {
        updateAuthStatus('需要登录', 'info');
        log('🔐 Auth event: showLogin');
      });

      window.addEventListener('auth:success', () => {
        updateAuthStatus('登录成功', 'success');
        log('✅ Auth event: success');
      });

      window.addEventListener('auth:logout', () => {
        updateAuthStatus('已登出', 'info');
        log('🚪 Auth event: logout');
      });
    }

    // 测试函数
    function testLogin() {
      log('🧪 Testing login...');
      if (window.authManager) {
        // 模拟登录成功
        window.authManager.onAuthSuccess();
        updateAuthStatus('模拟登录成功', 'success');
      } else {
        log('❌ AuthManager not available');
      }
    }

    function testLogout() {
      log('🧪 Testing logout...');
      if (window.authManager) {
        window.authManager.clearAuth();
        window.authManager.redirectToLogin();
        updateAuthStatus('模拟登出', 'info');
      } else {
        log('❌ AuthManager not available');
      }
    }

    function testAuthCheck() {
      log('🧪 Testing auth check...');
      if (window.authManager) {
        window.authManager.checkAuthStatus();
      } else {
        log('❌ AuthManager not available');
      }
    }

    function testRouteChange() {
      log('🧪 Testing route change...');
      const routes = ['/', '/subscription', '/buy', '/orders'];
      const randomRoute = routes[Math.floor(Math.random() * routes.length)];
      window.history.pushState({}, '', randomRoute);
      window.dispatchEvent(new PopStateEvent('popstate'));
      log(`🔄 Changed route to: ${randomRoute}`);
    }

    // 工具函数
    function log(message) {
      const logContainer = document.getElementById('test-log');
      const entry = document.createElement('div');
      entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logContainer.appendChild(entry);
      logContainer.scrollTop = logContainer.scrollHeight;
      console.log(message);
    }

    function updateAuthStatus(message, type) {
      const statusElement = document.getElementById('auth-status');
      statusElement.textContent = message;
      statusElement.className = `test-status status-${type}`;
    }

    function clearStatus() {
      document.getElementById('test-log').innerHTML = '';
      updateAuthStatus('状态已清空', 'info');
    }

    // 全局错误处理
    window.addEventListener('error', (event) => {
      log(`❌ Global error: ${event.error.message}`);
    });

    window.addEventListener('unhandledrejection', (event) => {
      log(`❌ Unhandled promise rejection: ${event.reason}`);
    });
    
    // 开始测试
    console.log('🚀 Starting HeroModern Route Test...');
    log('🚀 Starting route test...');
    waitForReact(loadAppScripts);
  </script>
</body>

</html>
