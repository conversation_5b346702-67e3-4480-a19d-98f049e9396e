/**
 * HeroModern Theme - Components Async
 * 异步加载的组件
 */

// 等待React加载完成
function waitForReact() {
  return new Promise((resolve, reject) => {
    if (typeof React !== 'undefined' && typeof ReactDOM !== 'undefined') {
      resolve();
    } else {
      let attempts = 0;
      const maxAttempts = 50; // 5秒超时

      const checkReact = setInterval(() => {
        attempts++;
        if (typeof React !== 'undefined' && typeof ReactDOM !== 'undefined') {
          clearInterval(checkReact);
          resolve();
        } else if (attempts >= maxAttempts) {
          clearInterval(checkReact);
          reject(new Error('React loading timeout'));
        }
      }, 100);
    }
  });
}

// 组件注册器
window.HeroComponents = {};

// 等待React加载后注册组件
waitForReact().then(() => {
  console.log('✅ React loaded, registering components...');
  const { useState, useEffect, useCallback, useMemo, useRef } = React;

  // 加载组件
  window.HeroComponents.LoadingSpinner = function({ size = 40, color = 'var(--heroui-primary)' }) {
    return React.createElement(
      'div',
      {
        className: 'hero-loading',
        style: {
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%'
        }
      },
      React.createElement('div', {
        className: 'hero-spinner',
        style: {
          width: `${size}px`,
          height: `${size}px`,
          border: `4px solid var(--heroui-content3)`,
          borderTop: `4px solid ${color}`,
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }
      })
    );
  };

  // 按钮组件
  window.HeroComponents.Button = function({ 
    children, 
    type = 'primary', 
    size = 'medium', 
    disabled = false, 
    loading = false,
    onClick,
    ...props 
  }) {
    const className = [
      'heroui-button',
      `heroui-button-${type}`,
      size !== 'medium' ? `heroui-button-${size}` : '',
      disabled ? 'disabled' : '',
      loading ? 'loading' : ''
    ].filter(Boolean).join(' ');

    return React.createElement(
      'button',
      {
        className,
        disabled: disabled || loading,
        onClick,
        ...props
      },
      loading && React.createElement(HeroComponents.LoadingSpinner, { size: 16 }),
      children
    );
  };

  // 卡片组件
  window.HeroComponents.Card = function({ children, header, className = '', ...props }) {
    return React.createElement(
      'div',
      {
        className: `heroui-card ${className}`,
        ...props
      },
      header && React.createElement(
        'div',
        { className: 'heroui-card-header' },
        typeof header === 'string' ? React.createElement('h3', null, header) : header
      ),
      React.createElement(
        'div',
        { className: 'heroui-card-body' },
        children
      )
    );
  };

  // 输入框组件
  window.HeroComponents.Input = function({ 
    label, 
    error, 
    className = '', 
    ...props 
  }) {
    return React.createElement(
      'div',
      { className: 'hero-form-group' },
      label && React.createElement('label', null, label),
      React.createElement('input', {
        className: `heroui-input ${error ? 'error' : ''} ${className}`,
        ...props
      }),
      error && React.createElement('span', { className: 'error-text' }, error)
    );
  };

  // 徽章组件
  window.HeroComponents.Badge = function({ children, type = 'secondary', className = '', ...props }) {
    return React.createElement(
      'span',
      {
        className: `heroui-badge heroui-badge-${type} ${className}`,
        ...props
      },
      children
    );
  };

  // 进度条组件
  window.HeroComponents.Progress = function({ value = 0, max = 100, className = '', ...props }) {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
    
    return React.createElement(
      'div',
      {
        className: `heroui-progress ${className}`,
        ...props
      },
      React.createElement('div', {
        className: 'heroui-progress-bar',
        style: { width: `${percentage}%` }
      })
    );
  };

  // 模态框组件
  window.HeroComponents.Modal = function({ 
    visible, 
    onClose, 
    title, 
    children, 
    footer,
    width = 520 
  }) {
    useEffect(() => {
      if (visible) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }
      
      return () => {
        document.body.style.overflow = '';
      };
    }, [visible]);

    if (!visible) return null;

    return React.createElement(
      'div',
      {
        className: 'hero-modal-overlay',
        style: {
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'var(--heroui-overlay)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        },
        onClick: (e) => {
          if (e.target === e.currentTarget) {
            onClose && onClose();
          }
        }
      },
      React.createElement(
        'div',
        {
          className: 'hero-modal',
          style: {
            background: 'var(--heroui-content1)',
            borderRadius: 'var(--heroui-radius-large)',
            width: `${width}px`,
            maxWidth: '90vw',
            maxHeight: '90vh',
            overflow: 'hidden',
            animation: 'fadeIn 0.3s ease'
          }
        },
        title && React.createElement(
          'div',
          {
            className: 'hero-modal-header',
            style: {
              padding: '16px 20px',
              borderBottom: '1px solid var(--heroui-divider)',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }
          },
          React.createElement('h3', { style: { margin: 0 } }, title),
          React.createElement(
            'button',
            {
              onClick: onClose,
              style: {
                background: 'none',
                border: 'none',
                fontSize: '20px',
                cursor: 'pointer',
                color: 'var(--heroui-content4)'
              }
            },
            '×'
          )
        ),
        React.createElement(
          'div',
          {
            className: 'hero-modal-body',
            style: {
              padding: '20px',
              maxHeight: '60vh',
              overflow: 'auto'
            }
          },
          children
        ),
        footer && React.createElement(
          'div',
          {
            className: 'hero-modal-footer',
            style: {
              padding: '16px 20px',
              borderTop: '1px solid var(--heroui-divider)',
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '8px'
            }
          },
          footer
        )
      )
    );
  };

  // 表格组件
  window.HeroComponents.Table = function({ columns, data, loading = false, className = '' }) {
    if (loading) {
      return React.createElement(HeroComponents.LoadingSpinner);
    }

    return React.createElement(
      'div',
      { className: `hero-table-container ${className}` },
      React.createElement(
        'table',
        {
          style: {
            width: '100%',
            borderCollapse: 'collapse'
          }
        },
        React.createElement(
          'thead',
          null,
          React.createElement(
            'tr',
            { style: { borderBottom: '1px solid var(--heroui-divider)' } },
            columns.map((column, index) =>
              React.createElement(
                'th',
                {
                  key: index,
                  style: {
                    padding: '12px',
                    textAlign: column.align || 'left',
                    fontWeight: '600'
                  }
                },
                column.title
              )
            )
          )
        ),
        React.createElement(
          'tbody',
          null,
          data.map((row, rowIndex) =>
            React.createElement(
              'tr',
              {
                key: rowIndex,
                style: { borderBottom: '1px solid var(--heroui-divider)' }
              },
              columns.map((column, colIndex) =>
                React.createElement(
                  'td',
                  {
                    key: colIndex,
                    style: {
                      padding: '12px',
                      textAlign: column.align || 'left'
                    }
                  },
                  column.render ? column.render(row[column.dataIndex], row, rowIndex) : row[column.dataIndex]
                )
              )
            )
          )
        )
      )
    );
  };

  // 空状态组件
  window.HeroComponents.Empty = function({ description = '暂无数据', image, action }) {
    return React.createElement(
      'div',
      {
        className: 'hero-empty',
        style: {
          textAlign: 'center',
          padding: '40px 20px',
          color: 'var(--heroui-content4)'
        }
      },
      image && React.createElement('div', { style: { marginBottom: '16px' } }, image),
      React.createElement('p', { style: { margin: '0 0 16px 0' } }, description),
      action && React.createElement('div', null, action)
    );
  };

  console.log('🎨 HeroModern Components loaded successfully');
});

// 导出组件加载状态
window.HeroComponents.ready = false;
waitForReact().then(() => {
  window.HeroComponents.ready = true;
  window.dispatchEvent(new CustomEvent('heroComponentsReady'));
  console.log('🎨 HeroModern Components ready');
}).catch((error) => {
  console.error('❌ Failed to load HeroModern Components:', error);
  window.dispatchEvent(new CustomEvent('heroComponentsError', { detail: error }));
});
