<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no" />
  <title>HeroModern Theme Test</title>

  <!-- HeroUI CSS -->
  <link rel="stylesheet" href="assets/heroui.min.css">
  <!-- Modern Theme CSS -->
  <link rel="stylesheet" href="assets/modern-theme.css">

  <style>
    :root {
      /* HeroUI 主题变量 */
      --heroui-primary: #006FEE;
      --heroui-secondary: #9353D3;
      --heroui-success: #17C964;
      --heroui-warning: #F5A524;
      --heroui-danger: #F31260;
      --heroui-foreground: #11181C;
      --heroui-background: #FFFFFF;
      --heroui-content1: #FFFFFF;
      --heroui-content2: #F4F4F5;
      --heroui-content3: #E4E4E7;
      --heroui-content4: #D4D4D8;
      --heroui-default: #D4D4D8;
      --heroui-divider: #E4E4E7;
      --heroui-focus: #006FEE;
      --heroui-overlay: rgba(0, 0, 0, 0.5);
      --heroui-radius-small: 8px;
      --heroui-radius-medium: 12px;
      --heroui-radius-large: 16px;
    }

    /* 暗色模式变量 */
    [data-theme="dark"] {
      --heroui-foreground: #ECEDEE;
      --heroui-background: #000000;
      --heroui-content1: #18181B;
      --heroui-content2: #27272A;
      --heroui-content3: #3F3F46;
      --heroui-content4: #52525B;
      --heroui-default: #3F3F46;
      --heroui-divider: #27272A;
      --heroui-overlay: rgba(0, 0, 0, 0.8);
    }

    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      background: var(--heroui-background);
      color: var(--heroui-foreground);
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* 背景渐变 */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--heroui-primary) 0%, var(--heroui-secondary) 100%);
      opacity: 0.05;
      z-index: -2;
    }

    /* 主应用容器 */
    #root {
      position: relative;
      z-index: 1;
      min-height: 100vh;
    }
  </style>
</head>

<body>
  <script>
    window.settings = {
      title: 'HeroModern Test',
      assets_path: 'assets',
      theme: {
        mode: 'auto',
        color: 'blue',
        sidebar_style: 'modern',
        navbar_style: 'floating',
        animations: true,
        blur: true,
        border_radius: 'medium'
      },
      version: '1.0.0',
      background_url: '',
      description: 'HeroModern Theme Test',
      i18n: [
        'zh-CN',
        'en-US',
        'ja-JP',
        'ko-KR',
        'zh-TW'
      ],
      logo: '',
      api: {
        base_url: '/api/v1',
        timeout: 30000
      }
    }

    // 主题模式检测和设置
    function initThemeMode() {
      const themeMode = window.settings.theme.mode;
      let isDark = false;
      
      if (themeMode === 'auto') {
        isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      } else {
        isDark = themeMode === 'dark';
      }
      
      document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
      
      // 监听系统主题变化
      if (themeMode === 'auto') {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
          document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        });
      }
    }

    // 初始化主题
    initThemeMode();
  </script>

  <!-- 应用根节点 -->
  <div id="root"></div>

  <!-- 国际化文件 -->
  <script src="assets/i18n/zh-CN.js"></script>
  <script src="assets/i18n/zh-TW.js"></script>
  <script src="assets/i18n/en-US.js"></script>
  <script src="assets/i18n/ja-JP.js"></script>
  <script src="assets/i18n/ko-KR.js"></script>

  <!-- React CDN -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

  <!-- 加载脚本 -->
  <script src="assets/heroui.min.js"></script>
  <script src="assets/modern-theme.js"></script>
  <script src="assets/hero-api.js"></script>
  <script src="assets/hero-pages.js"></script>
  <script src="assets/hero-app.js"></script>

  <script>
    // 测试通知
    setTimeout(() => {
      if (window.hero && window.hero.notify) {
        hero.notify('HeroModern 主题加载成功！', 'success');
      }
    }, 1000);
  </script>
</body>

</html>
