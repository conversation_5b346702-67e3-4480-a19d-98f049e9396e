/* <PERSON><PERSON> Simplified CSS - Modern Components */

/* CSS Variables for HeroUI Theme */
:root {
  --heroui-primary: #006FEE;
  --heroui-primary-50: #e6f1fe;
  --heroui-primary-100: #cce3fd;
  --heroui-primary-200: #99c7fb;
  --heroui-primary-300: #66aaf9;
  --heroui-primary-400: #338ef7;
  --heroui-primary-500: #006FEE;
  --heroui-primary-600: #005bc4;
  --heroui-primary-700: #004493;
  --heroui-primary-800: #002e62;
  --heroui-primary-900: #001731;

  --heroui-secondary: #9353D3;
  --heroui-success: #17C964;
  --heroui-warning: #F5A524;
  --heroui-danger: #F31260;

  --heroui-foreground: #11181C;
  --heroui-background: #FFFFFF;
  --heroui-content1: #FFFFFF;
  --heroui-content2: #F4F4F5;
  --heroui-content3: #E4E4E7;
  --heroui-content4: #D4D4D8;
  --heroui-default: #D4D4D8;
  --heroui-divider: #E4E4E7;
  --heroui-focus: #006FEE;
  --heroui-overlay: rgba(0, 0, 0, 0.5);

  --heroui-radius-small: 8px;
  --heroui-radius-medium: 12px;
  --heroui-radius-large: 16px;

  --heroui-shadow-small: 0px 0px 5px 0px rgb(0 0 0 / 0.02), 0px 2px 10px 0px rgb(0 0 0 / 0.06), 0px 0px 1px 0px rgb(0 0 0 / 0.3);
  --heroui-shadow-medium: 0px 0px 15px 0px rgb(0 0 0 / 0.03), 0px 2px 30px 0px rgb(0 0 0 / 0.08), 0px 0px 1px 0px rgb(0 0 0 / 0.3);
  --heroui-shadow-large: 0px 0px 30px 0px rgb(0 0 0 / 0.04), 0px 30px 60px 0px rgb(0 0 0 / 0.12), 0px 0px 1px 0px rgb(0 0 0 / 0.3);
}

[data-theme="dark"] {
  --heroui-foreground: #ECEDEE;
  --heroui-background: #000000;
  --heroui-content1: #18181B;
  --heroui-content2: #27272A;
  --heroui-content3: #3F3F46;
  --heroui-content4: #52525B;
  --heroui-default: #3F3F46;
  --heroui-divider: #27272A;
  --heroui-overlay: rgba(0, 0, 0, 0.8);
}

/* Button Component */
.heroui-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: var(--heroui-radius-medium);
  font-weight: 500;
  font-size: 14px;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.25s ease;
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.heroui-button:focus-visible {
  outline: 2px solid var(--heroui-focus);
  outline-offset: 2px;
}

.heroui-button-primary {
  background: var(--heroui-primary);
  color: white;
}

.heroui-button-primary:hover {
  background: var(--heroui-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--heroui-shadow-medium);
}

.heroui-button-secondary {
  background: var(--heroui-content2);
  color: var(--heroui-foreground);
}

.heroui-button-secondary:hover {
  background: var(--heroui-content3);
}

.heroui-button-success {
  background: var(--heroui-success);
  color: white;
}

.heroui-button-warning {
  background: var(--heroui-warning);
  color: white;
}

.heroui-button-danger {
  background: var(--heroui-danger);
  color: white;
}

.heroui-button-ghost {
  background: transparent;
  color: var(--heroui-primary);
  border: 1px solid var(--heroui-primary);
}

.heroui-button-ghost:hover {
  background: var(--heroui-primary);
  color: white;
}

.heroui-button-small {
  padding: 8px 16px;
  font-size: 12px;
}

.heroui-button-large {
  padding: 16px 24px;
  font-size: 16px;
}

/* Card Component */
.heroui-card {
  background: var(--heroui-content1);
  border: 1px solid var(--heroui-divider);
  border-radius: var(--heroui-radius-large);
  box-shadow: var(--heroui-shadow-small);
  overflow: hidden;
  transition: all 0.25s ease;
}

.heroui-card:hover {
  box-shadow: var(--heroui-shadow-medium);
  transform: translateY(-2px);
}

.heroui-card-header {
  padding: 20px 20px 0;
}

.heroui-card-body {
  padding: 20px;
}

.heroui-card-footer {
  padding: 0 20px 20px;
  border-top: 1px solid var(--heroui-divider);
  margin-top: 20px;
  padding-top: 20px;
}

/* Input Component */
.heroui-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--heroui-divider);
  border-radius: var(--heroui-radius-medium);
  background: var(--heroui-content1);
  color: var(--heroui-foreground);
  font-size: 14px;
  transition: all 0.25s ease;
  outline: none;
}

.heroui-input:focus {
  border-color: var(--heroui-primary);
  box-shadow: 0 0 0 3px rgba(0, 111, 238, 0.1);
}

.heroui-input::placeholder {
  color: var(--heroui-content4);
}

/* Avatar Component */
.heroui-avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--heroui-primary);
  color: white;
  font-weight: 500;
  overflow: hidden;
}

.heroui-avatar-small {
  width: 24px;
  height: 24px;
  font-size: 10px;
}

.heroui-avatar-medium {
  width: 32px;
  height: 32px;
  font-size: 12px;
}

.heroui-avatar-large {
  width: 48px;
  height: 48px;
  font-size: 16px;
}

/* Badge Component */
.heroui-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: var(--heroui-radius-small);
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
}

.heroui-badge-primary {
  background: var(--heroui-primary-100);
  color: var(--heroui-primary-700);
}

.heroui-badge-success {
  background: #dcfce7;
  color: #166534;
}

.heroui-badge-warning {
  background: #fef3c7;
  color: #92400e;
}

.heroui-badge-danger {
  background: #fecaca;
  color: #991b1b;
}

/* Chip Component */
.heroui-chip {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: var(--heroui-radius-large);
  background: var(--heroui-content2);
  color: var(--heroui-foreground);
  font-size: 12px;
  font-weight: 500;
}

.heroui-chip-close {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--heroui-content3);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

/* Progress Component */
.heroui-progress {
  width: 100%;
  height: 8px;
  background: var(--heroui-content2);
  border-radius: var(--heroui-radius-small);
  overflow: hidden;
}

.heroui-progress-bar {
  height: 100%;
  background: var(--heroui-primary);
  border-radius: var(--heroui-radius-small);
  transition: width 0.3s ease;
}

/* Spinner Component */
.heroui-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--heroui-content3);
  border-top: 2px solid var(--heroui-primary);
  border-radius: 50%;
  animation: heroui-spin 1s linear infinite;
}

@keyframes heroui-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.heroui-spinner-small {
  width: 16px;
  height: 16px;
}

.heroui-spinner-large {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

/* Divider Component */
.heroui-divider {
  border: none;
  height: 1px;
  background: var(--heroui-divider);
  margin: 16px 0;
}

.heroui-divider-vertical {
  width: 1px;
  height: auto;
  margin: 0 16px;
}

/* Utility Classes */
.heroui-text-primary { color: var(--heroui-primary); }
.heroui-text-secondary { color: var(--heroui-secondary); }
.heroui-text-success { color: var(--heroui-success); }
.heroui-text-warning { color: var(--heroui-warning); }
.heroui-text-danger { color: var(--heroui-danger); }
.heroui-text-foreground { color: var(--heroui-foreground); }

.heroui-bg-primary { background: var(--heroui-primary); }
.heroui-bg-secondary { background: var(--heroui-secondary); }
.heroui-bg-success { background: var(--heroui-success); }
.heroui-bg-warning { background: var(--heroui-warning); }
.heroui-bg-danger { background: var(--heroui-danger); }
.heroui-bg-content1 { background: var(--heroui-content1); }
.heroui-bg-content2 { background: var(--heroui-content2); }

.heroui-shadow-small { box-shadow: var(--heroui-shadow-small); }
.heroui-shadow-medium { box-shadow: var(--heroui-shadow-medium); }
.heroui-shadow-large { box-shadow: var(--heroui-shadow-large); }

.heroui-rounded-small { border-radius: var(--heroui-radius-small); }
.heroui-rounded-medium { border-radius: var(--heroui-radius-medium); }
.heroui-rounded-large { border-radius: var(--heroui-radius-large); }

/* Responsive utilities */
@media (max-width: 640px) {
  .heroui-button {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .heroui-card-body {
    padding: 16px;
  }
}
