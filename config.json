{"name": "HeroModern", "description": "基于 HeroUI 的现代化主题，具有简洁美观的设计和流畅的交互体验", "version": "1.0.0", "images": ["https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2160&q=80"], "configs": [{"label": "主题色", "placeholder": "请选择主题颜色", "field_name": "theme_color", "field_type": "select", "select_options": {"blue": "现代蓝", "purple": "优雅紫", "green": "自然绿", "orange": "活力橙", "pink": "温馨粉", "slate": "商务灰"}, "default_value": "blue"}, {"label": "界面模式", "placeholder": "请选择界面模式", "field_name": "theme_mode", "field_type": "select", "select_options": {"light": "亮色模式", "dark": "暗色模式", "auto": "跟随系统"}, "default_value": "auto"}, {"label": "边栏风格", "placeholder": "请选择边栏风格", "field_name": "sidebar_style", "field_type": "select", "select_options": {"modern": "现代风格", "minimal": "极简风格", "classic": "经典风格"}, "default_value": "modern"}, {"label": "导航栏风格", "placeholder": "请选择导航栏风格", "field_name": "navbar_style", "field_type": "select", "select_options": {"floating": "浮动式", "sticky": "固定式", "transparent": "透明式"}, "default_value": "floating"}, {"label": "背景图片", "placeholder": "请输入背景图片URL（留空使用默认渐变背景）", "field_name": "background_url", "field_type": "input"}, {"label": "动画效果", "placeholder": "是否启用动画效果", "field_name": "enable_animations", "field_type": "select", "select_options": {"true": "启用", "false": "禁用"}, "default_value": "true"}, {"label": "毛玻璃效果", "placeholder": "是否启用毛玻璃效果", "field_name": "enable_blur", "field_type": "select", "select_options": {"true": "启用", "false": "禁用"}, "default_value": "true"}, {"label": "圆角风格", "placeholder": "选择界面圆角风格", "field_name": "border_radius", "field_type": "select", "select_options": {"small": "小圆角", "medium": "中圆角", "large": "大圆角", "none": "无圆角"}, "default_value": "medium"}, {"label": "自定义页脚HTML", "placeholder": "可以实现客服JS代码的加入等", "field_name": "custom_html", "field_type": "textarea"}]}