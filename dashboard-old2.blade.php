<!DOCTYPE html>
<html>

<head>
  <!-- HeroModern Theme CSS -->
  <link rel="stylesheet" href="/theme/{{$theme}}/assets/components.chunk.css?v={{$version}}">
  <link rel="stylesheet" href="/theme/{{$theme}}/assets/umi.css?v={{$version}}">
  @if (file_exists(public_path("/theme/{$theme}/assets/custom.css")))
    <link rel="stylesheet" href="/theme/{{$theme}}/assets/custom.css?v={{$version}}">
  @endif
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
  
  @php ($colors = [
    'blue' => '#006FEE',
    'purple' => '#7C3AED', 
    'green' => '#17C964',
    'orange' => '#F5A524',
    'pink' => '#F31260',
    'slate' => '#64748B'
  ])
  <meta name="theme-color" content="{{$colors[$theme_config['theme_color'] ?? 'blue']}}">

  <title>{{$title}}</title>
  <script>window.routerBase = "/";</script>
  <script>
    window.settings = {
      title: '{{$title}}',
      assets_path: '/theme/{{$theme}}/assets',
      theme: {
        mode: '{{$theme_config['theme_mode'] ?? 'auto'}}',
        color: '{{$theme_config['theme_color'] ?? 'blue'}}',
        sidebar: '{{$theme_config['theme_sidebar'] ?? 'modern'}}',
        header: '{{$theme_config['theme_header'] ?? 'floating'}}',
        animations: {{$theme_config['enable_animations'] ?? 'true'}},
        blur: {{$theme_config['enable_blur'] ?? 'true'}},
        border_radius: '{{$theme_config['border_radius'] ?? 'medium'}}'
      },
      version: '{{$version}}',
      background_url: '{{$theme_config['background_url']}}',
      description: '{{$description}}',
      i18n: [
        'zh-CN',
        'zh-TW',
        'en-US', 
        'ja-JP',
        'ko-KR'
      ],
      logo: '{{$logo}}'
    }
  </script>
  
  <!-- React 依赖 - 必须在其他脚本之前加载 -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

  <!-- 国际化文件 -->
  <script src="/theme/{{$theme}}/assets/i18n/zh-CN.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/zh-TW.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/en-US.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/ja-JP.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/ko-KR.js?v={{$version}}"></script>
</head>

<body>
  <div id="root"></div>
  {!! $theme_config['custom_html'] ?? '' !!}

  <!-- UMI 脚本 - 按正确顺序加载 -->
  <script src="/theme/{{$theme}}/assets/vendors.async.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/components.async.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/umi.js?v={{$version}}"></script>
  
  @if (file_exists(public_path("/theme/{$theme}/assets/custom.js")))
    <script src="/theme/{{$theme}}/assets/custom.js?v={{$version}}"></script>
  @endif
</body>

</html>
