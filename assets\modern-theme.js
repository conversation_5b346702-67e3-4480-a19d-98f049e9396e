/**
 * HeroModern Theme - Modern UI JavaScript
 * 基于 HeroUI 的现代化主题脚本
 */

(function() {
  'use strict';

  // 主题配置
  const themeConfig = window.settings?.theme || {};
  
  // 主题管理器
  class HeroThemeManager {
    constructor() {
      this.init();
    }

    init() {
      this.setupThemeMode();
      this.setupSidebar();
      this.setupNavbar();
      this.setupAnimations();
      this.setupResponsive();
      this.bindEvents();
    }

    // 主题模式设置
    setupThemeMode() {
      const mode = themeConfig.mode || 'auto';
      
      if (mode === 'auto') {
        this.watchSystemTheme();
      } else {
        this.setTheme(mode);
      }
    }

    // 监听系统主题变化
    watchSystemTheme() {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const updateTheme = (e) => {
        this.setTheme(e.matches ? 'dark' : 'light');
      };
      
      updateTheme(mediaQuery);
      mediaQuery.addEventListener('change', updateTheme);
    }

    // 设置主题
    setTheme(theme) {
      document.documentElement.setAttribute('data-theme', theme);
      
      // 更新 meta theme-color
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        const colors = {
          blue: theme === 'dark' ? '#006FEE' : '#006FEE',
          purple: theme === 'dark' ? '#7C3AED' : '#7C3AED',
          green: theme === 'dark' ? '#17C964' : '#17C964',
          orange: theme === 'dark' ? '#F5A524' : '#F5A524',
          pink: theme === 'dark' ? '#F31260' : '#F31260',
          slate: theme === 'dark' ? '#64748B' : '#64748B'
        };
        metaThemeColor.content = colors[themeConfig.color] || colors.blue;
      }
    }

    // 侧边栏设置
    setupSidebar() {
      const sidebar = document.querySelector('.hero-sidebar');
      if (!sidebar) return;

      // 应用侧边栏样式
      const style = themeConfig.sidebar_style || 'modern';
      sidebar.classList.add(style);

      // 侧边栏折叠状态
      const isCollapsed = localStorage.getItem('hero-sidebar-collapsed') === 'true';
      if (isCollapsed) {
        sidebar.classList.add('collapsed');
      }
    }

    // 导航栏设置
    setupNavbar() {
      const navbar = document.querySelector('.hero-navbar');
      if (!navbar) return;

      // 应用导航栏样式
      const style = themeConfig.navbar_style || 'floating';
      navbar.classList.add(style);

      // 毛玻璃效果
      if (themeConfig.blur && style === 'transparent') {
        navbar.classList.add('hero-blur');
      }
    }

    // 动画设置
    setupAnimations() {
      if (themeConfig.animations === false) {
        document.body.classList.add('no-animations');
        return;
      }

      // 为页面元素添加动画
      this.addScrollAnimations();
    }

    // 滚动动画
    addScrollAnimations() {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('hero-animate-fade');
          }
        });
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      });

      // 观察所有卡片元素
      document.querySelectorAll('.hero-card').forEach(card => {
        observer.observe(card);
      });
    }

    // 响应式设置
    setupResponsive() {
      this.handleResize();
      window.addEventListener('resize', () => this.handleResize());
    }

    handleResize() {
      const isMobile = window.innerWidth <= 768;
      const sidebar = document.querySelector('.hero-sidebar');
      
      if (sidebar) {
        if (isMobile) {
          sidebar.classList.remove('collapsed');
        }
      }
    }

    // 绑定事件
    bindEvents() {
      this.bindSidebarToggle();
      this.bindThemeToggle();
      this.bindUserMenu();
    }

    // 侧边栏切换
    bindSidebarToggle() {
      const toggleBtn = document.querySelector('.hero-toggle-btn');
      const sidebar = document.querySelector('.hero-sidebar');
      
      if (!toggleBtn || !sidebar) return;

      toggleBtn.addEventListener('click', () => {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
          sidebar.classList.toggle('open');
        } else {
          sidebar.classList.toggle('collapsed');
          localStorage.setItem('hero-sidebar-collapsed', sidebar.classList.contains('collapsed'));
        }
      });

      // 移动端点击遮罩关闭侧边栏
      document.addEventListener('click', (e) => {
        const isMobile = window.innerWidth <= 768;
        if (isMobile && sidebar.classList.contains('open')) {
          if (!sidebar.contains(e.target) && !toggleBtn.contains(e.target)) {
            sidebar.classList.remove('open');
          }
        }
      });
    }

    // 主题切换
    bindThemeToggle() {
      const themeToggle = document.querySelector('.hero-theme-toggle');
      if (!themeToggle) return;

      themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
        localStorage.setItem('hero-theme', newTheme);
      });
    }

    // 用户菜单
    bindUserMenu() {
      const userMenu = document.querySelector('.hero-user-menu');
      if (!userMenu) return;

      userMenu.addEventListener('click', () => {
        // 这里可以添加用户菜单的逻辑
        console.log('User menu clicked');
      });
    }
  }

  // 工具函数
  const HeroUtils = {
    // 防抖函数
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // 节流函数
    throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    // 平滑滚动
    smoothScrollTo(element, duration = 300) {
      const targetPosition = element.offsetTop;
      const startPosition = window.pageYOffset;
      const distance = targetPosition - startPosition;
      let startTime = null;

      function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = this.easeInOutQuad(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
      }

      requestAnimationFrame(animation);
    },

    // 缓动函数
    easeInOutQuad(t, b, c, d) {
      t /= d / 2;
      if (t < 1) return c / 2 * t * t + b;
      t--;
      return -c / 2 * (t * (t - 2) - 1) + b;
    },

    // 获取随机颜色
    getRandomColor() {
      const colors = ['blue', 'purple', 'green', 'orange', 'pink', 'slate'];
      return colors[Math.floor(Math.random() * colors.length)];
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const seconds = String(d.getSeconds()).padStart(2, '0');

      return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
    }
  };

  // 通知系统
  class HeroNotification {
    static show(message, type = 'info', duration = 3000) {
      const notification = document.createElement('div');
      notification.className = `hero-notification hero-notification-${type}`;
      notification.textContent = message;

      // 添加样式
      Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: 'var(--heroui-radius-medium)',
        color: 'white',
        fontWeight: '500',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        maxWidth: '300px',
        wordWrap: 'break-word'
      });

      // 设置背景色
      const colors = {
        info: 'var(--heroui-primary)',
        success: 'var(--heroui-success)',
        warning: 'var(--heroui-warning)',
        error: 'var(--heroui-danger)'
      };
      notification.style.background = colors[type] || colors.info;

      document.body.appendChild(notification);

      // 显示动画
      setTimeout(() => {
        notification.style.transform = 'translateX(0)';
      }, 10);

      // 自动隐藏
      setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }, duration);

      return notification;
    }
  }

  // 初始化主题
  document.addEventListener('DOMContentLoaded', () => {
    window.HeroTheme = new HeroThemeManager();
    window.HeroUtils = HeroUtils;
    window.HeroNotification = HeroNotification;
    
    // 全局可用
    window.hero = {
      theme: window.HeroTheme,
      utils: HeroUtils,
      notify: HeroNotification.show
    };
  });

})();
