/**
 * HeroModern Theme - UMI Application
 * 基于 UMI 框架的 v2board 前端应用
 * 兼容 v2board 的路由和API系统
 */

(function() {
  'use strict';

  // 等待所有依赖加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initUMIApp);
  } else {
    initUMIApp();
  }

  function initUMIApp() {
    console.log('🚀 HeroModern UMI Application Starting...');

    // 检查依赖
    if (typeof React === 'undefined' || typeof ReactDOM === 'undefined') {
      console.error('React or ReactDOM not loaded. Please check script loading order.');
      showErrorMessage('React 依赖加载失败，请刷新页面重试');
      return;
    }

    console.log('✅ React dependencies loaded successfully');

    // 初始化主题
    initTheme();

    // 检查认证状态
    checkAuthAndRoute();
  }

  function showErrorMessage(message) {
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.innerHTML = `
        <div style="
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
          background: #ffffff;
          color: #11181c;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        ">
          <div style="
            text-align: center;
            padding: 40px;
            border: 1px solid #e4e4e7;
            border-radius: 12px;
            background: #ffffff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          ">
            <h2 style="margin: 0 0 16px 0; color: #f31260;">⚠️ 加载错误</h2>
            <p style="margin: 0 0 20px 0; color: #71717a;">${message}</p>
            <button onclick="window.location.reload()" style="
              background: #006fee;
              color: white;
              border: none;
              border-radius: 8px;
              padding: 8px 16px;
              cursor: pointer;
              font-size: 14px;
            ">刷新页面</button>
          </div>
        </div>
      `;
    }
  }

  function initTheme() {
    // 应用主题设置
    if (window.settings && window.settings.theme) {
      const { theme } = window.settings;
      
      // 设置主题模式
      if (theme.mode === 'auto') {
        const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
        
        // 监听系统主题变化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
          document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        });
      } else {
        document.documentElement.setAttribute('data-theme', theme.mode);
      }

      // 应用主题色彩
      if (window.HeroUtils && window.HeroUtils.applyThemeColor) {
        window.HeroUtils.applyThemeColor(theme.color || 'blue');
      }
    }
  }

  async function checkAuthAndRoute() {
    console.log('🔐 Checking authentication status...');

    try {
      // 检查是否已登录
      const authResult = await fetch('/api/v1/user/checkLogin', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      console.log('Auth check response status:', authResult.status);

      if (authResult.ok) {
        const authData = await authResult.json();
        console.log('Auth data:', authData);

        if (authData && authData.success) {
          console.log('✅ User is authenticated, loading main app');
          loadMainApp(true); // 传递认证状态
        } else {
          console.log('❌ User not authenticated, showing login in main app');
          loadMainApp(false); // 传递未认证状态
        }
      } else {
        console.log('⚠️ Auth check request failed, showing login in main app');
        loadMainApp(false);
      }
    } catch (error) {
      console.error('❌ Auth check failed:', error);
      // 网络错误，显示主应用但显示登录界面
      console.log('🔧 Loading main app with login form due to network error');
      loadMainApp(false);
    }
  }

  function loadMainApp(isAuthenticated = null) {
    console.log('📱 Loading main application...', { isAuthenticated });

    try {
      // 创建主应用，传递认证状态
      const App = React.createElement(MainApplication, {
        initialAuthState: isAuthenticated
      });

      // 渲染到根节点
      const rootElement = document.getElementById('root');
      if (rootElement) {
        if (ReactDOM.createRoot) {
          const root = ReactDOM.createRoot(rootElement);
          root.render(App);
          console.log('✅ Main app rendered with createRoot');
        } else {
          ReactDOM.render(App, rootElement);
          console.log('✅ Main app rendered with legacy render');
        }
      } else {
        console.error('❌ Root element not found');
        showErrorMessage('应用容器未找到');
      }
    } catch (error) {
      console.error('❌ Failed to load main app:', error);
      showErrorMessage(`主应用加载失败: ${error.message}`);
    }
  }



  // 主应用组件
  function MainApplication({ initialAuthState }) {
    const [currentRoute, setCurrentRoute] = React.useState(getCurrentRoute());
    const [user, setUser] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [isAuthenticated, setIsAuthenticated] = React.useState(initialAuthState);
    const [showLogin, setShowLogin] = React.useState(initialAuthState === false);

    React.useEffect(() => {
      // 监听路由变化
      window.addEventListener('popstate', handleRouteChange);

      // 监听认证事件
      window.addEventListener('auth:showLogin', () => {
        setShowLogin(true);
        setIsAuthenticated(false);
      });

      window.addEventListener('auth:success', () => {
        setShowLogin(false);
        setIsAuthenticated(true);
        loadUserInfo();
      });

      // 如果已认证，加载用户信息
      if (isAuthenticated) {
        loadUserInfo();
      } else {
        setLoading(false);
      }

      return () => {
        window.removeEventListener('popstate', handleRouteChange);
        window.removeEventListener('auth:showLogin', () => {});
        window.removeEventListener('auth:success', () => {});
      };
    }, [isAuthenticated]);

    const handleRouteChange = () => {
      setCurrentRoute(getCurrentRoute());
    };

    const navigate = (path) => {
      window.history.pushState({}, '', path);
      setCurrentRoute(path);
    };

    const loadUserInfo = async () => {
      try {
        const response = await fetch('/api/v1/user/info', {
          credentials: 'include'
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setUser(result.data);
          }
        }
      } catch (error) {
        console.error('Failed to load user info:', error);
      } finally {
        setLoading(false);
      }
    };

    if (loading) {
      return React.createElement(
        'div',
        { className: 'hero-loading' },
        React.createElement('div', { className: 'hero-spinner' })
      );
    }

    // 如果需要显示登录界面
    if (showLogin || !isAuthenticated) {
      return React.createElement(LoginApplication, {
        onLoginSuccess: () => {
          setShowLogin(false);
          setIsAuthenticated(true);
          loadUserInfo();
        }
      });
    }

    // 根据路由渲染对应组件
    const renderCurrentPage = () => {
      // 如果页面组件存在，使用它们
      if (window.Dashboard && currentRoute === '/') {
        return React.createElement(window.Dashboard, { user, navigate });
      }
      if (window.MySubscription && currentRoute === '/subscription') {
        return React.createElement(window.MySubscription, { user, navigate });
      }
      if (window.BuySubscription && currentRoute === '/buy') {
        return React.createElement(window.BuySubscription, { user, navigate });
      }
      if (window.TrafficLog && currentRoute === '/traffic') {
        return React.createElement(window.TrafficLog, { user, navigate });
      }
      if (window.MyOrders && currentRoute === '/orders') {
        return React.createElement(window.MyOrders, { user, navigate });
      }
      if (window.MyInvites && currentRoute === '/invites') {
        return React.createElement(window.MyInvites, { user, navigate });
      }
      if (window.MyTickets && currentRoute === '/tickets') {
        return React.createElement(window.MyTickets, { user, navigate });
      }
      if (window.Knowledge && currentRoute === '/knowledge') {
        return React.createElement(window.Knowledge, { user, navigate });
      }
      if (window.Profile && currentRoute === '/profile') {
        return React.createElement(window.Profile, { user, navigate });
      }

      // 默认显示仪表板
      return React.createElement(
        'div',
        { style: { padding: '20px', textAlign: 'center' } },
        React.createElement('h2', null, '欢迎使用 HeroModern 主题'),
        React.createElement('p', null, `当前路由: ${currentRoute}`),
        React.createElement('p', null, `认证状态: ${isAuthenticated ? '已登录' : '未登录'}`),
        React.createElement('p', null, '页面组件正在加载中...')
      );
    };

    return React.createElement(
      'div',
      { className: 'hero-app' },
      renderCurrentPage()
    );
  }

  // 登录应用组件
  function LoginApplication({ onLoginSuccess }) {
    const [currentForm, setCurrentForm] = React.useState('login');

    const handleLoginSuccess = () => {
      console.log('🎉 Login successful');
      if (onLoginSuccess) {
        onLoginSuccess();
      } else {
        // 备用方案：重新加载页面
        window.location.reload();
      }
    };

    const renderForm = () => {
      // 如果认证组件存在，使用它们
      if (window.LoginForm && currentForm === 'login') {
        return React.createElement(window.LoginForm, {
          onSuccess: handleLoginSuccess,
          onSwitchToRegister: () => setCurrentForm('register'),
          onSwitchToForgot: () => setCurrentForm('forgot')
        });
      }
      if (window.RegisterForm && currentForm === 'register') {
        return React.createElement(window.RegisterForm, {
          onSuccess: () => setCurrentForm('login'),
          onSwitchToLogin: () => setCurrentForm('login')
        });
      }
      if (window.ForgotPasswordForm && currentForm === 'forgot') {
        return React.createElement(window.ForgotPasswordForm, {
          onSuccess: () => setCurrentForm('login'),
          onSwitchToLogin: () => setCurrentForm('login')
        });
      }

      // 简单的登录表单
      return React.createElement(
        'div',
        { style: { padding: '40px', textAlign: 'center' } },
        React.createElement('h2', null, '登录'),
        React.createElement('p', null, '请登录以继续使用'),
        React.createElement(
          'button',
          {
            onClick: () => window.location.href = '/login',
            style: {
              padding: '8px 16px',
              background: 'var(--heroui-primary)',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }
          },
          '前往登录页面'
        )
      );
    };

    return React.createElement(
      'div',
      {
        className: 'hero-login-app',
        style: {
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }
      },
      React.createElement(
        'div',
        {
          style: {
            background: 'var(--heroui-content1)',
            padding: '40px',
            borderRadius: 'var(--heroui-radius-large)',
            boxShadow: '0 8px 40px rgba(0, 0, 0, 0.1)',
            maxWidth: '400px',
            width: '100%'
          }
        },
        renderForm()
      )
    );
  }

  // 获取当前路由
  function getCurrentRoute() {
    return window.location.pathname;
  }

  // 全局错误处理
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    if (window.HeroUtils && window.HeroUtils.notify) {
      window.HeroUtils.notify('应用出现错误，请刷新页面', 'error');
    }
  });

  console.log('✅ HeroModern UMI Application loaded');

})();
