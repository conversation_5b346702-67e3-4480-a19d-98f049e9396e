<?php
/**
 * HeroModern Theme Installer
 * 自动安装和配置 HeroModern 主题
 */

class HeroModernInstaller
{
    private $themeName = 'HeroModern';
    private $themeDir;
    private $publicDir;
    
    public function __construct()
    {
        $this->themeDir = __DIR__;
        $this->publicDir = dirname(dirname(__DIR__)) . '/public/theme/' . $this->themeName;
    }
    
    /**
     * 安装主题
     */
    public function install()
    {
        echo "开始安装 HeroModern 主题...\n";
        
        try {
            // 检查环境
            $this->checkEnvironment();
            
            // 复制文件到 public 目录
            $this->copyFiles();
            
            // 设置权限
            $this->setPermissions();
            
            // 验证安装
            $this->verifyInstallation();
            
            echo "✅ HeroModern 主题安装成功！\n";
            echo "请在管理后台选择 'HeroModern' 主题并配置相关选项。\n";
            
        } catch (Exception $e) {
            echo "❌ 安装失败: " . $e->getMessage() . "\n";
            return false;
        }
        
        return true;
    }
    
    /**
     * 卸载主题
     */
    public function uninstall()
    {
        echo "开始卸载 HeroModern 主题...\n";
        
        try {
            if (is_dir($this->publicDir)) {
                $this->removeDirectory($this->publicDir);
                echo "✅ HeroModern 主题卸载成功！\n";
            } else {
                echo "ℹ️ 主题文件不存在，无需卸载。\n";
            }
        } catch (Exception $e) {
            echo "❌ 卸载失败: " . $e->getMessage() . "\n";
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查环境
     */
    private function checkEnvironment()
    {
        // 检查 PHP 版本
        if (version_compare(PHP_VERSION, '7.4.0', '<')) {
            throw new Exception('需要 PHP 7.4.0 或更高版本');
        }
        
        // 检查必要的扩展
        $requiredExtensions = ['json', 'mbstring'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                throw new Exception("缺少必要的 PHP 扩展: {$ext}");
            }
        }
        
        // 检查目录权限
        $publicThemeDir = dirname($this->publicDir);
        if (!is_dir($publicThemeDir)) {
            if (!mkdir($publicThemeDir, 0755, true)) {
                throw new Exception("无法创建目录: {$publicThemeDir}");
            }
        }
        
        if (!is_writable($publicThemeDir)) {
            throw new Exception("目录不可写: {$publicThemeDir}");
        }
        
        echo "✅ 环境检查通过\n";
    }
    
    /**
     * 复制文件
     */
    private function copyFiles()
    {
        echo "正在复制主题文件...\n";
        
        // 创建目标目录
        if (!is_dir($this->publicDir)) {
            mkdir($this->publicDir, 0755, true);
        }
        
        // 复制文件
        $this->copyDirectory($this->themeDir, $this->publicDir, [
            'install.php',
            'demo.html',
            'README.md'
        ]);
        
        echo "✅ 文件复制完成\n";
    }
    
    /**
     * 设置权限
     */
    private function setPermissions()
    {
        echo "正在设置文件权限...\n";
        
        // 设置目录权限
        chmod($this->publicDir, 0755);
        
        // 递归设置文件权限
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->publicDir)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                chmod($file->getPathname(), 0644);
            } elseif ($file->isDir()) {
                chmod($file->getPathname(), 0755);
            }
        }
        
        echo "✅ 权限设置完成\n";
    }
    
    /**
     * 验证安装
     */
    private function verifyInstallation()
    {
        echo "正在验证安装...\n";
        
        $requiredFiles = [
            'config.json',
            'dashboard.blade.php',
            'assets/heroui.min.css',
            'assets/heroui.min.js',
            'assets/modern-theme.css',
            'assets/modern-theme.js'
        ];
        
        foreach ($requiredFiles as $file) {
            $filePath = $this->publicDir . '/' . $file;
            if (!file_exists($filePath)) {
                throw new Exception("缺少必要文件: {$file}");
            }
        }
        
        // 验证配置文件
        $configPath = $this->publicDir . '/config.json';
        $config = json_decode(file_get_contents($configPath), true);
        if (!$config || $config['name'] !== $this->themeName) {
            throw new Exception('配置文件无效');
        }
        
        echo "✅ 安装验证通过\n";
    }
    
    /**
     * 复制目录
     */
    private function copyDirectory($source, $destination, $exclude = [])
    {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $relativePath = substr($item->getPathname(), strlen($source) + 1);
            
            // 跳过排除的文件
            if (in_array(basename($item->getPathname()), $exclude)) {
                continue;
            }
            
            $target = $destination . DIRECTORY_SEPARATOR . $relativePath;
            
            if ($item->isDir()) {
                if (!is_dir($target)) {
                    mkdir($target, 0755, true);
                }
            } else {
                copy($item->getPathname(), $target);
            }
        }
    }
    
    /**
     * 删除目录
     */
    private function removeDirectory($dir)
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $item) {
            if ($item->isDir()) {
                rmdir($item->getPathname());
            } else {
                unlink($item->getPathname());
            }
        }
        
        rmdir($dir);
    }
}

// 命令行执行
if (php_sapi_name() === 'cli') {
    $installer = new HeroModernInstaller();
    
    $action = $argv[1] ?? 'install';
    
    switch ($action) {
        case 'install':
            $installer->install();
            break;
        case 'uninstall':
            $installer->uninstall();
            break;
        default:
            echo "用法: php install.php [install|uninstall]\n";
            break;
    }
}
?>
