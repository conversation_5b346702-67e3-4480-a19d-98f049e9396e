// 한국어 언어팩
window.i18n = window.i18n || {};
window.i18n['ko-KR'] = {
  // 공통
  common: {
    confirm: '확인',
    cancel: '취소',
    save: '저장',
    delete: '삭제',
    edit: '편집',
    add: '추가',
    search: '검색',
    loading: '로딩 중...',
    success: '성공',
    error: '오류',
    warning: '경고',
    info: '정보',
    close: '닫기',
    back: '뒤로',
    next: '다음',
    previous: '이전',
    submit: '제출',
    reset: '재설정',
    refresh: '새로고침',
    copy: '복사',
    download: '다운로드',
    upload: '업로드',
    view: '보기',
    settings: '설정',
    help: '도움말',
    logout: '로그아웃',
    login: '로그인',
    register: '회원가입'
  },

  // 내비게이션
  nav: {
    dashboard: '대시보드',
    subscription: '구독',
    traffic: '트래픽',
    invite: '초대',
    profile: '프로필',
    billing: '결제',
    ticket: '티켓',
    knowledge: '지식베이스',
    admin: '관리'
  },

  // 대시보드
  dashboard: {
    title: '대시보드',
    welcome: '환영합니다',
    overview: '개요',
    quickActions: '빠른 작업',
    recentActivity: '최근 활동',
    statistics: '통계',
    announcements: '공지사항',
    systemStatus: '시스템 상태'
  },

  // 구독
  subscription: {
    title: '내 구독',
    current: '현재 구독',
    expired: '만료됨',
    active: '활성',
    inactive: '비활성',
    renew: '갱신',
    upgrade: '업그레이드',
    downgrade: '다운그레이드',
    cancel: '구독 취소',
    details: '구독 상세',
    history: '구독 기록',
    plans: '요금제'
  },

  // 트래픽
  traffic: {
    title: '트래픽 통계',
    used: '사용됨',
    remaining: '남은 양',
    total: '총계',
    upload: '업로드',
    download: '다운로드',
    today: '오늘',
    thisMonth: '이번 달',
    lastMonth: '지난 달',
    reset: '재설정 시간',
    unlimited: '무제한'
  },

  // 초대
  invite: {
    title: '친구 초대',
    code: '초대 코드',
    link: '초대 링크',
    commission: '수수료',
    invitees: '초대한 사람 수',
    earnings: '수익',
    generate: '코드 생성',
    share: '링크 공유',
    history: '초대 기록'
  },

  // 프로필
  profile: {
    title: '프로필',
    avatar: '아바타',
    username: '사용자명',
    email: '이메일',
    phone: '전화번호',
    password: '비밀번호',
    changePassword: '비밀번호 변경',
    currentPassword: '현재 비밀번호',
    newPassword: '새 비밀번호',
    confirmPassword: '비밀번호 확인',
    updateProfile: '프로필 업데이트',
    twoFactor: '2단계 인증',
    enable: '활성화',
    disable: '비활성화'
  },

  // 결제
  billing: {
    title: '결제 관리',
    balance: '잔액',
    recharge: '충전',
    withdraw: '출금',
    history: '거래 기록',
    invoice: '청구서',
    payment: '결제',
    amount: '금액',
    method: '결제 방법',
    status: '상태',
    date: '날짜',
    pending: '대기 중',
    completed: '완료됨',
    failed: '실패'
  },

  // 티켓
  ticket: {
    title: '티켓 시스템',
    create: '티켓 생성',
    subject: '제목',
    priority: '우선순위',
    status: '상태',
    category: '카테고리',
    description: '설명',
    attachment: '첨부파일',
    reply: '답변',
    close: '티켓 닫기',
    open: '열림',
    closed: '닫힘',
    pending: '대기 중',
    resolved: '해결됨',
    low: '낮음',
    medium: '보통',
    high: '높음',
    urgent: '긴급'
  },

  // 지식베이스
  knowledge: {
    title: '지식베이스',
    search: '문서 검색',
    categories: '카테고리',
    popular: '인기 문서',
    recent: '최신 문서',
    helpful: '도움됨',
    notHelpful: '도움 안됨',
    feedback: '피드백'
  },

  // 폼 검증
  validation: {
    required: '이 필드는 필수입니다',
    email: '유효한 이메일 주소를 입력하세요',
    password: '비밀번호는 최소 8자 이상이어야 합니다',
    confirmPassword: '비밀번호가 일치하지 않습니다',
    phone: '유효한 전화번호를 입력하세요',
    url: '유효한 URL을 입력하세요',
    number: '유효한 숫자를 입력하세요',
    min: '최솟값은 {min}입니다',
    max: '최댓값은 {max}입니다',
    minLength: '최소 {min}자 이상 입력하세요',
    maxLength: '최대 {max}자까지 입력 가능합니다'
  },

  // 메시지
  message: {
    saveSuccess: '저장되었습니다',
    saveFailed: '저장에 실패했습니다',
    deleteSuccess: '삭제되었습니다',
    deleteFailed: '삭제에 실패했습니다',
    updateSuccess: '업데이트되었습니다',
    updateFailed: '업데이트에 실패했습니다',
    uploadSuccess: '업로드되었습니다',
    uploadFailed: '업로드에 실패했습니다',
    copySuccess: '복사되었습니다',
    copyFailed: '복사에 실패했습니다',
    networkError: '네트워크 오류입니다. 나중에 다시 시도하세요',
    serverError: '서버 오류',
    unauthorized: '인증되지 않은 접근',
    forbidden: '접근이 금지되었습니다',
    notFound: '페이지를 찾을 수 없습니다',
    timeout: '요청 시간 초과'
  },

  // 시간
  time: {
    now: '방금 전',
    minutesAgo: '{n}분 전',
    hoursAgo: '{n}시간 전',
    daysAgo: '{n}일 전',
    weeksAgo: '{n}주 전',
    monthsAgo: '{n}개월 전',
    yearsAgo: '{n}년 전',
    second: '초',
    minute: '분',
    hour: '시간',
    day: '일',
    week: '주',
    month: '월',
    year: '년'
  }
};
