// 日本語言語パック
window.i18n = window.i18n || {};
window.i18n['ja-JP'] = {
  // 共通
  common: {
    confirm: '確認',
    cancel: 'キャンセル',
    save: '保存',
    delete: '削除',
    edit: '編集',
    add: '追加',
    search: '検索',
    loading: '読み込み中...',
    success: '成功',
    error: 'エラー',
    warning: '警告',
    info: '情報',
    close: '閉じる',
    back: '戻る',
    next: '次へ',
    previous: '前へ',
    submit: '送信',
    reset: 'リセット',
    refresh: '更新',
    copy: 'コピー',
    download: 'ダウンロード',
    upload: 'アップロード',
    view: '表示',
    settings: '設定',
    help: 'ヘルプ',
    logout: 'ログアウト',
    login: 'ログイン',
    register: '登録'
  },

  // ナビゲーション
  nav: {
    dashboard: 'ダッシュボード',
    subscription: 'サブスクリプション',
    traffic: 'トラフィック',
    invite: '招待',
    profile: 'プロフィール',
    billing: '請求',
    ticket: 'チケット',
    knowledge: 'ナレッジ',
    admin: '管理'
  },

  // ダッシュボード
  dashboard: {
    title: 'ダッシュボード',
    welcome: 'おかえりなさい',
    overview: '概要',
    quickActions: 'クイックアクション',
    recentActivity: '最近のアクティビティ',
    statistics: '統計',
    announcements: 'お知らせ',
    systemStatus: 'システム状態'
  },

  // サブスクリプション
  subscription: {
    title: 'マイサブスクリプション',
    current: '現在のサブスクリプション',
    expired: '期限切れ',
    active: 'アクティブ',
    inactive: '非アクティブ',
    renew: '更新',
    upgrade: 'アップグレード',
    downgrade: 'ダウングレード',
    cancel: 'サブスクリプションキャンセル',
    details: 'サブスクリプション詳細',
    history: 'サブスクリプション履歴',
    plans: 'プラン'
  },

  // トラフィック
  traffic: {
    title: 'トラフィック統計',
    used: '使用済み',
    remaining: '残り',
    total: '合計',
    upload: 'アップロード',
    download: 'ダウンロード',
    today: '今日',
    thisMonth: '今月',
    lastMonth: '先月',
    reset: 'リセット時間',
    unlimited: '無制限'
  },

  // 招待
  invite: {
    title: '友達を招待',
    code: '招待コード',
    link: '招待リンク',
    commission: 'コミッション',
    invitees: '招待者数',
    earnings: '収益',
    generate: 'コード生成',
    share: 'リンク共有',
    history: '招待履歴'
  },

  // プロフィール
  profile: {
    title: 'プロフィール',
    avatar: 'アバター',
    username: 'ユーザー名',
    email: 'メール',
    phone: '電話番号',
    password: 'パスワード',
    changePassword: 'パスワード変更',
    currentPassword: '現在のパスワード',
    newPassword: '新しいパスワード',
    confirmPassword: 'パスワード確認',
    updateProfile: 'プロフィール更新',
    twoFactor: '二段階認証',
    enable: '有効',
    disable: '無効'
  },

  // 請求
  billing: {
    title: '請求管理',
    balance: '残高',
    recharge: 'チャージ',
    withdraw: '出金',
    history: '取引履歴',
    invoice: '請求書',
    payment: '支払い',
    amount: '金額',
    method: '支払い方法',
    status: 'ステータス',
    date: '日付',
    pending: '保留中',
    completed: '完了',
    failed: '失敗'
  },

  // チケット
  ticket: {
    title: 'チケットシステム',
    create: 'チケット作成',
    subject: '件名',
    priority: '優先度',
    status: 'ステータス',
    category: 'カテゴリ',
    description: '説明',
    attachment: '添付ファイル',
    reply: '返信',
    close: 'チケットを閉じる',
    open: 'オープン',
    closed: 'クローズ',
    pending: '保留中',
    resolved: '解決済み',
    low: '低',
    medium: '中',
    high: '高',
    urgent: '緊急'
  },

  // ナレッジ
  knowledge: {
    title: 'ナレッジベース',
    search: '記事検索',
    categories: 'カテゴリ',
    popular: '人気記事',
    recent: '最新記事',
    helpful: '役に立った',
    notHelpful: '役に立たなかった',
    feedback: 'フィードバック'
  },

  // フォーム検証
  validation: {
    required: 'この項目は必須です',
    email: '有効なメールアドレスを入力してください',
    password: 'パスワードは8文字以上である必要があります',
    confirmPassword: 'パスワードが一致しません',
    phone: '有効な電話番号を入力してください',
    url: '有効なURLを入力してください',
    number: '有効な数値を入力してください',
    min: '最小値は {min} です',
    max: '最大値は {max} です',
    minLength: '最小文字数は {min} 文字です',
    maxLength: '最大文字数は {max} 文字です'
  },

  // メッセージ
  message: {
    saveSuccess: '保存に成功しました',
    saveFailed: '保存に失敗しました',
    deleteSuccess: '削除に成功しました',
    deleteFailed: '削除に失敗しました',
    updateSuccess: '更新に成功しました',
    updateFailed: '更新に失敗しました',
    uploadSuccess: 'アップロードに成功しました',
    uploadFailed: 'アップロードに失敗しました',
    copySuccess: 'コピーに成功しました',
    copyFailed: 'コピーに失敗しました',
    networkError: 'ネットワークエラーです。後でもう一度お試しください',
    serverError: 'サーバーエラー',
    unauthorized: '認証されていないアクセス',
    forbidden: 'アクセスが禁止されています',
    notFound: 'ページが見つかりません',
    timeout: 'リクエストタイムアウト'
  },

  // 時間
  time: {
    now: 'たった今',
    minutesAgo: '{n}分前',
    hoursAgo: '{n}時間前',
    daysAgo: '{n}日前',
    weeksAgo: '{n}週間前',
    monthsAgo: '{n}ヶ月前',
    yearsAgo: '{n}年前',
    second: '秒',
    minute: '分',
    hour: '時間',
    day: '日',
    week: '週',
    month: '月',
    year: '年'
  }
};
