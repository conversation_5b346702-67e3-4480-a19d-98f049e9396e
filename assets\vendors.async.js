/**
 * HeroModern Theme - Vendors Async
 * 第三方库和依赖
 */

// 检查React依赖
if (typeof React === 'undefined' || typeof ReactDOM === 'undefined') {
  console.error('React or ReactDOM not loaded. Please ensure React is loaded before this script.');
}

// 工具函数库
window.HeroUtils = {
  // 格式化字节
  formatBytes: (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 格式化日期
  formatDate: (date) => {
    return new Date(date).toLocaleString('zh-CN');
  },

  // 格式化货币
  formatCurrency: (amount) => {
    return `¥${parseFloat(amount).toFixed(2)}`;
  },

  // 防抖函数
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // 节流函数
  throttle: (func, limit) => {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  },

  // 深拷贝
  deepClone: (obj) => {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => HeroUtils.deepClone(item));
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = HeroUtils.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  },

  // 生成UUID
  generateUUID: () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },

  // 获取查询参数
  getQueryParam: (name) => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
  },

  // 设置查询参数
  setQueryParam: (name, value) => {
    const url = new URL(window.location);
    url.searchParams.set(name, value);
    window.history.pushState({}, '', url);
  },

  // 移除查询参数
  removeQueryParam: (name) => {
    const url = new URL(window.location);
    url.searchParams.delete(name);
    window.history.pushState({}, '', url);
  },

  // 检查是否为移动设备
  isMobile: () => {
    return window.innerWidth <= 768;
  },

  // 检查是否为暗色模式
  isDarkMode: () => {
    return document.documentElement.getAttribute('data-theme') === 'dark';
  },

  // 切换主题模式
  toggleTheme: () => {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('hero-theme', newTheme);
    return newTheme;
  },

  // 应用主题色彩
  applyThemeColor: (color) => {
    const colorMap = {
      blue: { primary: '#006FEE', secondary: '#338EF7' },
      purple: { primary: '#7C3AED', secondary: '#9353D3' },
      green: { primary: '#17C964', secondary: '#45D483' },
      orange: { primary: '#F5A524', secondary: '#F7B955' },
      pink: { primary: '#F31260', secondary: '#F54180' },
      slate: { primary: '#64748B', secondary: '#94A3B8' }
    };

    const colors = colorMap[color] || colorMap.blue;
    document.documentElement.style.setProperty('--heroui-primary', colors.primary);
    document.documentElement.style.setProperty('--heroui-secondary', colors.secondary);
    
    // 移除旧的主题类
    document.body.classList.remove('theme-blue', 'theme-purple', 'theme-green', 'theme-orange', 'theme-pink', 'theme-slate');
    // 添加新的主题类
    document.body.classList.add(`theme-${color}`);
  },

  // 显示通知
  notify: (message, type = 'info', duration = 3000) => {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `hero-notification hero-notification-${type}`;
    notification.innerHTML = `
      <div class="hero-notification-content">
        <span class="hero-notification-icon">${getNotificationIcon(type)}</span>
        <span class="hero-notification-message">${message}</span>
        <button class="hero-notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;

    // 添加样式
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      background: var(--heroui-content1);
      border: 1px solid var(--heroui-divider);
      border-radius: var(--heroui-radius-medium);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 12px 16px;
      max-width: 400px;
      animation: slideInRight 0.3s ease;
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 自动移除
    setTimeout(() => {
      if (notification.parentElement) {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => notification.remove(), 300);
      }
    }, duration);

    function getNotificationIcon(type) {
      const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
      };
      return icons[type] || icons.info;
    }
  },

  // 复制到剪贴板
  copyToClipboard: async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      HeroUtils.notify('复制成功', 'success');
      return true;
    } catch (err) {
      console.error('复制失败:', err);
      HeroUtils.notify('复制失败', 'error');
      return false;
    }
  },

  // 下载文件
  downloadFile: (url, filename) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },

  // 格式化相对时间
  formatRelativeTime: (date) => {
    const now = new Date();
    const diff = now - new Date(date);
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (seconds < 60) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return HeroUtils.formatDate(date);
  }
};

// 添加通知样式
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  .hero-notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .hero-notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--heroui-content4);
    margin-left: auto;
  }

  .hero-notification-close:hover {
    color: var(--heroui-foreground);
  }
`;
document.head.appendChild(notificationStyles);

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

console.log('🚀 HeroModern Vendors loaded successfully');
