/**
 * HeroModern Theme - API Service
 * 完整的API服务实现，参考CyberPunk主题
 */

(function() {
  'use strict';

  // API配置
  const API_CONFIG = {
    baseURL: '/api/v1',
    timeout: window.settings?.api?.timeout || 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  };

  // 获取认证token
  function getAuthToken() {
    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token') || '';
  }

  // HTTP请求封装
  async function request(url, options = {}) {
    const config = {
      method: 'GET',
      headers: {
        ...API_CONFIG.headers,
        'Authorization': `Bearer ${getAuthToken()}`
      },
      ...options
    };

    // 添加CSRF token如果存在
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRF-TOKEN'] = csrfToken;
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);

      const response = await fetch(`${API_CONFIG.baseURL}${url}`, {
        ...config,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return { success: true, data: data.data || data, message: data.message };
    } catch (error) {
      console.error('API Request Error:', error);
      return { 
        success: false, 
        data: null, 
        message: error.message || '网络请求失败' 
      };
    }
  }

  // API服务类
  class HeroAPI {
    // ========== Passport 相关API ==========

    // 获取配置
    static async getConfig() {
      return await request('/passport/comm/config');
    }

    // 登录校验
    static async checkAuth() {
      return await request('/passport/auth/check');
    }

    // 登录账号
    static async login(credentials) {
      return await request('/passport/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials)
      });
    }

    // 发送邮箱验证码
    static async sendEmailVerify(email) {
      return await request('/passport/comm/sendEmailVerify', {
        method: 'POST',
        body: JSON.stringify({ email })
      });
    }

    // 注册账号
    static async register(userData) {
      return await request('/passport/auth/register', {
        method: 'POST',
        body: JSON.stringify(userData)
      });
    }

    // 重置密码
    static async forgotPassword(resetData) {
      return await request('/passport/auth/forget', {
        method: 'POST',
        body: JSON.stringify(resetData)
      });
    }

    // ========== User 相关API ==========

    // 登出
    static async logout() {
      return await request('/user/logout');
    }

    // 获取用户信息
    static async getUser() {
      return await request('/user/info');
    }

    // 获取订阅信息
    static async getSubscription() {
      return await request('/user/getSubscribe');
    }

    // 重置订阅链接
    static async resetSubscribeUrl() {
      return await request('/user/resetSecurity');
    }

    // 获取代办事项
    static async getDashboardStats() {
      return await request('/user/getStat');
    }

    // 修改密码
    static async changePassword(passwordData) {
      return await request('/user/changePassword', {
        method: 'POST',
        body: JSON.stringify(passwordData)
      });
    }

    // 更新通知状态
    static async updateUser(userData) {
      return await request('/user/update', {
        method: 'POST',
        body: JSON.stringify(userData)
      });
    }

    // 佣金划转
    static async transferCommission(amount) {
      return await request('/user/transfer', {
        method: 'POST',
        body: JSON.stringify({ transfer_amount: amount })
      });
    }

    // ========== Plan 相关API ==========

    // 获取套餐列表
    static async getPlans() {
      return await request('/user/plan/fetch');
    }

    // ========== Order 相关API ==========

    // 获取订单列表
    static async getOrders() {
      return await request('/user/order/fetch');
    }

    // 获取支付方式
    static async getPaymentMethods() {
      return await request('/user/order/getPaymentMethod');
    }

    // 获取订单详情
    static async getOrderDetail(tradeNo) {
      return await request(`/user/order/details?trade_no=${tradeNo}`);
    }

    // 检查订单状态
    static async checkOrderStatus(tradeNo) {
      return await request(`/user/order/check?trade_no=${tradeNo}`);
    }

    // 创建订单
    static async createOrder(orderData) {
      return await request('/user/order/save', {
        method: 'POST',
        body: JSON.stringify(orderData)
      });
    }

    // 结算订单
    static async checkoutOrder(checkoutData) {
      return await request('/user/order/checkout', {
        method: 'POST',
        body: JSON.stringify(checkoutData)
      });
    }

    // 取消订单
    static async cancelOrder(tradeNo) {
      return await request('/user/order/cancel', {
        method: 'POST',
        body: JSON.stringify({ trade_no: tradeNo })
      });
    }

    // ========== Server 相关API ==========

    // 获取服务器列表
    static async getServers() {
      return await request('/user/server/fetch');
    }

    // 获取流量记录
    static async getTrafficLog() {
      return await request('/user/server/log/fetch');
    }

    // ========== Invite 相关API ==========

    // 获取邀请信息
    static async getInviteData() {
      return await request('/user/invite/fetch');
    }

    // 保存邀请码
    static async generateInviteCode() {
      return await request('/user/invite/save', {
        method: 'POST'
      });
    }

    // ========== Ticket 相关API ==========

    // 获取工单列表
    static async getTickets() {
      return await request('/user/ticket/fetch');
    }

    // 创建工单
    static async createTicket(ticketData) {
      return await request('/user/ticket/save', {
        method: 'POST',
        body: JSON.stringify(ticketData)
      });
    }

    // 回复工单
    static async replyTicket(ticketId, message) {
      return await request('/user/ticket/reply', {
        method: 'POST',
        body: JSON.stringify({
          id: ticketId,
          message: message
        })
      });
    }

    // 关闭工单
    static async closeTicket(ticketId) {
      return await request('/user/ticket/close', {
        method: 'POST',
        body: JSON.stringify({ id: ticketId })
      });
    }

    // ========== Knowledge 相关API ==========

    // 获取知识库文章
    static async getKnowledgeArticles() {
      return await request('/user/knowledge/fetch');
    }

    // ========== Notice 相关API ==========

    // 获取公告
    static async getNotices() {
      return await request('/user/notice/fetch');
    }

    // ========== Coupon 相关API ==========

    // 检查优惠券
    static async checkCoupon(code) {
      return await request('/user/coupon/check', {
        method: 'POST',
        body: JSON.stringify({ code })
      });
    }

    // ========== 便捷方法 ==========

    // 购买套餐（组合方法）
    static async purchasePlan(planId, period, paymentMethod, couponCode = null) {
      try {
        // 1. 创建订单
        const orderData = {
          plan_id: planId,
          period: period
        };

        if (couponCode) {
          orderData.coupon_code = couponCode;
        }

        const orderResult = await this.createOrder(orderData);
        if (!orderResult.success) {
          return orderResult;
        }

        // 2. 结算订单
        const checkoutData = {
          trade_no: orderResult.data,
          method: paymentMethod
        };

        return await this.checkoutOrder(checkoutData);
      } catch (error) {
        return {
          success: false,
          message: error.message || '购买失败'
        };
      }
    }
  }

  // 错误处理
  class APIError extends Error {
    constructor(message, code, data) {
      super(message);
      this.name = 'APIError';
      this.code = code;
      this.data = data;
    }
  }

  // 请求拦截器
  function setupInterceptors() {
    // 响应拦截器 - 处理通用错误
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
      try {
        const response = await originalFetch.apply(this, args);
        
        // 处理401未授权
        if (response.status === 401) {
          localStorage.removeItem('auth_token');
          sessionStorage.removeItem('auth_token');
          
          if (window.hero && window.hero.notify) {
            hero.notify('登录已过期，请重新登录', 'warning');
          }
          
          // 跳转到登录页面
          setTimeout(() => {
            window.location.href = '/login';
          }, 2000);
        }
        
        return response;
      } catch (error) {
        console.error('Network Error:', error);
        throw error;
      }
    };
  }

  // 初始化API服务
  function initAPI() {
    setupInterceptors();
    
    // 检查登录状态
    if (getAuthToken()) {
      console.log('🔐 User authenticated');
    } else {
      console.log('🔓 User not authenticated');
    }
  }

  // 导出API服务
  window.HeroAPI = HeroAPI;
  window.APIError = APIError;

  // 初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAPI);
  } else {
    initAPI();
  }

})();
