/**
 * HeroModern Theme - Authentication Components
 * 完整的登录、注册、忘记密码功能
 */

(function() {
  'use strict';

  // 认证状态管理
  const authState = {
    isAuthenticated: false,
    user: null,
    token: null,
    loading: false
  };

  // 认证管理器
  class AuthManager {
    constructor() {
      this.init();
    }

    init() {
      this.checkAuthStatus();
      this.bindEvents();
    }

    // 检查认证状态
    async checkAuthStatus() {
      const token = this.getToken();
      if (!token) {
        this.redirectToLogin();
        return;
      }

      try {
        const result = await window.HeroAPI.checkAuth();
        if (result.success) {
          authState.isAuthenticated = true;
          authState.user = result.data;
          this.onAuthSuccess();
        } else {
          this.clearAuth();
          this.redirectToLogin();
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        this.clearAuth();
        this.redirectToLogin();
      }
    }

    // 登录
    async login(credentials) {
      authState.loading = true;
      
      try {
        const result = await window.HeroAPI.login(credentials);
        
        if (result.success) {
          this.setToken(result.data.auth_data);
          authState.isAuthenticated = true;
          authState.user = result.data;
          this.onAuthSuccess();
          return { success: true };
        } else {
          return { 
            success: false, 
            message: result.message || '登录失败' 
          };
        }
      } catch (error) {
        console.error('Login failed:', error);
        return { 
          success: false, 
          message: error.message || '网络错误' 
        };
      } finally {
        authState.loading = false;
      }
    }

    // 注册
    async register(userData) {
      authState.loading = true;
      
      try {
        const result = await window.HeroAPI.register(userData);
        
        if (result.success) {
          return { 
            success: true, 
            message: '注册成功，请登录' 
          };
        } else {
          return { 
            success: false, 
            message: result.message || '注册失败' 
          };
        }
      } catch (error) {
        console.error('Register failed:', error);
        return { 
          success: false, 
          message: error.message || '网络错误' 
        };
      } finally {
        authState.loading = false;
      }
    }

    // 忘记密码
    async forgotPassword(resetData) {
      authState.loading = true;
      
      try {
        const result = await window.HeroAPI.forgotPassword(resetData);
        
        if (result.success) {
          return { 
            success: true, 
            message: '密码重置邮件已发送' 
          };
        } else {
          return { 
            success: false, 
            message: result.message || '重置失败' 
          };
        }
      } catch (error) {
        console.error('Forgot password failed:', error);
        return { 
          success: false, 
          message: error.message || '网络错误' 
        };
      } finally {
        authState.loading = false;
      }
    }

    // 登出
    async logout() {
      try {
        await window.HeroAPI.logout();
      } catch (error) {
        console.error('Logout API failed:', error);
      } finally {
        this.clearAuth();
        this.redirectToLogin();
      }
    }

    // 发送邮箱验证码
    async sendEmailVerify(email) {
      try {
        const result = await window.HeroAPI.sendEmailVerify(email);
        return result;
      } catch (error) {
        console.error('Send email verify failed:', error);
        return { 
          success: false, 
          message: error.message || '发送失败' 
        };
      }
    }

    // Token 管理
    getToken() {
      return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    }

    setToken(token) {
      localStorage.setItem('auth_token', token);
      authState.token = token;
    }

    clearAuth() {
      localStorage.removeItem('auth_token');
      sessionStorage.removeItem('auth_token');
      authState.isAuthenticated = false;
      authState.user = null;
      authState.token = null;
    }

    // 重定向到登录页面
    redirectToLogin() {
      // v2board 使用前端路由，不需要重定向到 /login
      // 而是触发前端路由切换到登录状态
      console.log('🔐 User not authenticated, showing login form');

      // 触发登录状态事件
      window.dispatchEvent(new CustomEvent('auth:showLogin'));

      // 如果当前不在根路径，则导航到根路径
      if (window.location.pathname !== '/') {
        window.history.pushState({}, '', '/');
      }
    }

    // 认证成功回调
    onAuthSuccess() {
      console.log('🔐 Authentication successful');

      // 触发认证成功事件
      window.dispatchEvent(new CustomEvent('auth:success'));

      // 不需要重定向，让前端路由处理
      console.log('✅ User authenticated, loading main application');
    }

    // 绑定事件
    bindEvents() {
      // 监听token过期
      window.addEventListener('auth:expired', () => {
        this.clearAuth();
        this.redirectToLogin();
      });

      // 监听登出事件
      window.addEventListener('auth:logout', () => {
        this.logout();
      });
    }

    // 获取用户信息
    getUser() {
      return authState.user;
    }

    // 检查是否已认证
    isAuthenticated() {
      return authState.isAuthenticated;
    }

    // 检查是否正在加载
    isLoading() {
      return authState.loading;
    }
  }

  // 登录组件
  window.LoginForm = function({ onSuccess, onSwitchToRegister, onSwitchToForgot }) {
    const [formData, setFormData] = React.useState({
      email: '',
      password: '',
      remember: false
    });
    const [loading, setLoading] = React.useState(false);
    const [errors, setErrors] = React.useState({});

    const handleSubmit = async (e) => {
      e.preventDefault();
      setErrors({});
      setLoading(true);

      // 表单验证
      const newErrors = {};
      if (!formData.email) {
        newErrors.email = '请输入邮箱';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = '邮箱格式不正确';
      }
      if (!formData.password) {
        newErrors.password = '请输入密码';
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        setLoading(false);
        return;
      }

      try {
        const result = await window.authManager.login(formData);
        
        if (result.success) {
          if (window.hero && window.hero.notify) {
            hero.notify('登录成功', 'success');
          }
          if (onSuccess) onSuccess();
        } else {
          if (window.hero && window.hero.notify) {
            hero.notify(result.message, 'error');
          }
        }
      } catch (error) {
        console.error('Login error:', error);
        if (window.hero && window.hero.notify) {
          hero.notify('登录失败，请稍后重试', 'error');
        }
      } finally {
        setLoading(false);
      }
    };

    return React.createElement(
      'form',
      { onSubmit: handleSubmit, className: 'hero-auth-form' },
      React.createElement('h2', { className: 'hero-auth-title' }, '登录账户'),
      
      // 邮箱输入
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '邮箱'),
        React.createElement('input', {
          type: 'email',
          className: `heroui-input ${errors.email ? 'error' : ''}`,
          value: formData.email,
          onChange: (e) => setFormData({ ...formData, email: e.target.value }),
          placeholder: '请输入邮箱地址'
        }),
        errors.email && React.createElement('span', { className: 'error-text' }, errors.email)
      ),

      // 密码输入
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '密码'),
        React.createElement('input', {
          type: 'password',
          className: `heroui-input ${errors.password ? 'error' : ''}`,
          value: formData.password,
          onChange: (e) => setFormData({ ...formData, password: e.target.value }),
          placeholder: '请输入密码'
        }),
        errors.password && React.createElement('span', { className: 'error-text' }, errors.password)
      ),

      // 记住我
      React.createElement(
        'div',
        { className: 'hero-form-group hero-checkbox-group' },
        React.createElement('label', { className: 'hero-checkbox-label' },
          React.createElement('input', {
            type: 'checkbox',
            checked: formData.remember,
            onChange: (e) => setFormData({ ...formData, remember: e.target.checked })
          }),
          React.createElement('span', null, '记住我')
        )
      ),

      // 提交按钮
      React.createElement(
        'button',
        {
          type: 'submit',
          className: 'heroui-button heroui-button-primary',
          style: { width: '100%' },
          disabled: loading
        },
        loading ? '登录中...' : '登录'
      ),

      // 链接
      React.createElement(
        'div',
        { className: 'hero-auth-links' },
        React.createElement(
          'a',
          { href: '#', onClick: (e) => { e.preventDefault(); onSwitchToForgot(); } },
          '忘记密码？'
        ),
        React.createElement(
          'a',
          { href: '#', onClick: (e) => { e.preventDefault(); onSwitchToRegister(); } },
          '注册账户'
        )
      )
    );
  };

  // 注册组件
  window.RegisterForm = function({ onSuccess, onSwitchToLogin }) {
    const [formData, setFormData] = React.useState({
      email: '',
      password: '',
      password_confirmation: '',
      invite_code: '',
      email_code: '',
      agree: false
    });
    const [loading, setLoading] = React.useState(false);
    const [emailLoading, setEmailLoading] = React.useState(false);
    const [errors, setErrors] = React.useState({});
    const [emailSent, setEmailSent] = React.useState(false);

    const handleSendEmailCode = async () => {
      if (!formData.email) {
        setErrors({ email: '请先输入邮箱' });
        return;
      }

      setEmailLoading(true);
      try {
        const result = await window.authManager.sendEmailVerify(formData.email);
        if (result.success) {
          setEmailSent(true);
          if (window.hero && window.hero.notify) {
            hero.notify('验证码已发送', 'success');
          }
        } else {
          if (window.hero && window.hero.notify) {
            hero.notify(result.message || '发送失败', 'error');
          }
        }
      } catch (error) {
        if (window.hero && window.hero.notify) {
          hero.notify('发送失败，请稍后重试', 'error');
        }
      } finally {
        setEmailLoading(false);
      }
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      setErrors({});
      setLoading(true);

      // 表单验证
      const newErrors = {};
      if (!formData.email) {
        newErrors.email = '请输入邮箱';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = '邮箱格式不正确';
      }
      if (!formData.password) {
        newErrors.password = '请输入密码';
      } else if (formData.password.length < 8) {
        newErrors.password = '密码至少8位';
      }
      if (formData.password !== formData.password_confirmation) {
        newErrors.password_confirmation = '两次密码不一致';
      }
      if (!formData.email_code) {
        newErrors.email_code = '请输入邮箱验证码';
      }
      if (!formData.agree) {
        newErrors.agree = '请同意服务条款';
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        setLoading(false);
        return;
      }

      try {
        const result = await window.authManager.register(formData);

        if (result.success) {
          if (window.hero && window.hero.notify) {
            hero.notify(result.message || '注册成功', 'success');
          }
          if (onSuccess) onSuccess();
        } else {
          if (window.hero && window.hero.notify) {
            hero.notify(result.message, 'error');
          }
        }
      } catch (error) {
        console.error('Register error:', error);
        if (window.hero && window.hero.notify) {
          hero.notify('注册失败，请稍后重试', 'error');
        }
      } finally {
        setLoading(false);
      }
    };

    return React.createElement(
      'form',
      { onSubmit: handleSubmit, className: 'hero-auth-form' },
      React.createElement('h2', { className: 'hero-auth-title' }, '注册账户'),

      // 邮箱输入
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '邮箱'),
        React.createElement('input', {
          type: 'email',
          className: `heroui-input ${errors.email ? 'error' : ''}`,
          value: formData.email,
          onChange: (e) => setFormData({ ...formData, email: e.target.value }),
          placeholder: '请输入邮箱地址'
        }),
        errors.email && React.createElement('span', { className: 'error-text' }, errors.email)
      ),

      // 邮箱验证码
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '邮箱验证码'),
        React.createElement(
          'div',
          { className: 'hero-input-group' },
          React.createElement('input', {
            type: 'text',
            className: `heroui-input ${errors.email_code ? 'error' : ''}`,
            value: formData.email_code,
            onChange: (e) => setFormData({ ...formData, email_code: e.target.value }),
            placeholder: '请输入验证码'
          }),
          React.createElement(
            'button',
            {
              type: 'button',
              className: 'heroui-button heroui-button-secondary',
              onClick: handleSendEmailCode,
              disabled: emailLoading || !formData.email
            },
            emailLoading ? '发送中...' : emailSent ? '重新发送' : '发送验证码'
          )
        ),
        errors.email_code && React.createElement('span', { className: 'error-text' }, errors.email_code)
      ),

      // 密码输入
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '密码'),
        React.createElement('input', {
          type: 'password',
          className: `heroui-input ${errors.password ? 'error' : ''}`,
          value: formData.password,
          onChange: (e) => setFormData({ ...formData, password: e.target.value }),
          placeholder: '请输入密码（至少8位）'
        }),
        errors.password && React.createElement('span', { className: 'error-text' }, errors.password)
      ),

      // 确认密码
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '确认密码'),
        React.createElement('input', {
          type: 'password',
          className: `heroui-input ${errors.password_confirmation ? 'error' : ''}`,
          value: formData.password_confirmation,
          onChange: (e) => setFormData({ ...formData, password_confirmation: e.target.value }),
          placeholder: '请再次输入密码'
        }),
        errors.password_confirmation && React.createElement('span', { className: 'error-text' }, errors.password_confirmation)
      ),

      // 邀请码（可选）
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '邀请码（可选）'),
        React.createElement('input', {
          type: 'text',
          className: 'heroui-input',
          value: formData.invite_code,
          onChange: (e) => setFormData({ ...formData, invite_code: e.target.value }),
          placeholder: '请输入邀请码'
        })
      ),

      // 同意条款
      React.createElement(
        'div',
        { className: 'hero-form-group hero-checkbox-group' },
        React.createElement('label', { className: 'hero-checkbox-label' },
          React.createElement('input', {
            type: 'checkbox',
            checked: formData.agree,
            onChange: (e) => setFormData({ ...formData, agree: e.target.checked })
          }),
          React.createElement('span', null, '我同意服务条款和隐私政策')
        ),
        errors.agree && React.createElement('span', { className: 'error-text' }, errors.agree)
      ),

      // 提交按钮
      React.createElement(
        'button',
        {
          type: 'submit',
          className: 'heroui-button heroui-button-primary',
          style: { width: '100%' },
          disabled: loading
        },
        loading ? '注册中...' : '注册'
      ),

      // 链接
      React.createElement(
        'div',
        { className: 'hero-auth-links' },
        React.createElement(
          'a',
          { href: '#', onClick: (e) => { e.preventDefault(); onSwitchToLogin(); } },
          '已有账户？立即登录'
        )
      )
    );
  };

  // 忘记密码组件
  window.ForgotPasswordForm = function({ onSuccess, onSwitchToLogin }) {
    const [formData, setFormData] = React.useState({
      email: '',
      email_code: '',
      password: '',
      password_confirmation: ''
    });
    const [loading, setLoading] = React.useState(false);
    const [emailLoading, setEmailLoading] = React.useState(false);
    const [errors, setErrors] = React.useState({});
    const [emailSent, setEmailSent] = React.useState(false);

    const handleSendEmailCode = async () => {
      if (!formData.email) {
        setErrors({ email: '请先输入邮箱' });
        return;
      }

      setEmailLoading(true);
      try {
        const result = await window.authManager.sendEmailVerify(formData.email);
        if (result.success) {
          setEmailSent(true);
          if (window.hero && window.hero.notify) {
            hero.notify('验证码已发送', 'success');
          }
        } else {
          if (window.hero && window.hero.notify) {
            hero.notify(result.message || '发送失败', 'error');
          }
        }
      } catch (error) {
        if (window.hero && window.hero.notify) {
          hero.notify('发送失败，请稍后重试', 'error');
        }
      } finally {
        setEmailLoading(false);
      }
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      setErrors({});
      setLoading(true);

      // 表单验证
      const newErrors = {};
      if (!formData.email) {
        newErrors.email = '请输入邮箱';
      }
      if (!formData.email_code) {
        newErrors.email_code = '请输入邮箱验证码';
      }
      if (!formData.password) {
        newErrors.password = '请输入新密码';
      } else if (formData.password.length < 8) {
        newErrors.password = '密码至少8位';
      }
      if (formData.password !== formData.password_confirmation) {
        newErrors.password_confirmation = '两次密码不一致';
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        setLoading(false);
        return;
      }

      try {
        const result = await window.authManager.forgotPassword(formData);

        if (result.success) {
          if (window.hero && window.hero.notify) {
            hero.notify(result.message || '密码重置成功', 'success');
          }
          if (onSuccess) onSuccess();
        } else {
          if (window.hero && window.hero.notify) {
            hero.notify(result.message, 'error');
          }
        }
      } catch (error) {
        console.error('Forgot password error:', error);
        if (window.hero && window.hero.notify) {
          hero.notify('重置失败，请稍后重试', 'error');
        }
      } finally {
        setLoading(false);
      }
    };

    return React.createElement(
      'form',
      { onSubmit: handleSubmit, className: 'hero-auth-form' },
      React.createElement('h2', { className: 'hero-auth-title' }, '重置密码'),

      // 邮箱输入
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '邮箱'),
        React.createElement('input', {
          type: 'email',
          className: `heroui-input ${errors.email ? 'error' : ''}`,
          value: formData.email,
          onChange: (e) => setFormData({ ...formData, email: e.target.value }),
          placeholder: '请输入邮箱地址'
        }),
        errors.email && React.createElement('span', { className: 'error-text' }, errors.email)
      ),

      // 邮箱验证码
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '邮箱验证码'),
        React.createElement(
          'div',
          { className: 'hero-input-group' },
          React.createElement('input', {
            type: 'text',
            className: `heroui-input ${errors.email_code ? 'error' : ''}`,
            value: formData.email_code,
            onChange: (e) => setFormData({ ...formData, email_code: e.target.value }),
            placeholder: '请输入验证码'
          }),
          React.createElement(
            'button',
            {
              type: 'button',
              className: 'heroui-button heroui-button-secondary',
              onClick: handleSendEmailCode,
              disabled: emailLoading || !formData.email
            },
            emailLoading ? '发送中...' : emailSent ? '重新发送' : '发送验证码'
          )
        ),
        errors.email_code && React.createElement('span', { className: 'error-text' }, errors.email_code)
      ),

      // 新密码
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '新密码'),
        React.createElement('input', {
          type: 'password',
          className: `heroui-input ${errors.password ? 'error' : ''}`,
          value: formData.password,
          onChange: (e) => setFormData({ ...formData, password: e.target.value }),
          placeholder: '请输入新密码（至少8位）'
        }),
        errors.password && React.createElement('span', { className: 'error-text' }, errors.password)
      ),

      // 确认新密码
      React.createElement(
        'div',
        { className: 'hero-form-group' },
        React.createElement('label', null, '确认新密码'),
        React.createElement('input', {
          type: 'password',
          className: `heroui-input ${errors.password_confirmation ? 'error' : ''}`,
          value: formData.password_confirmation,
          onChange: (e) => setFormData({ ...formData, password_confirmation: e.target.value }),
          placeholder: '请再次输入新密码'
        }),
        errors.password_confirmation && React.createElement('span', { className: 'error-text' }, errors.password_confirmation)
      ),

      // 提交按钮
      React.createElement(
        'button',
        {
          type: 'submit',
          className: 'heroui-button heroui-button-primary',
          style: { width: '100%' },
          disabled: loading
        },
        loading ? '重置中...' : '重置密码'
      ),

      // 链接
      React.createElement(
        'div',
        { className: 'hero-auth-links' },
        React.createElement(
          'a',
          { href: '#', onClick: (e) => { e.preventDefault(); onSwitchToLogin(); } },
          '返回登录'
        )
      )
    );
  };

  // 初始化认证管理器
  window.authManager = new AuthManager();

  // 导出认证状态
  window.authState = authState;

})();
