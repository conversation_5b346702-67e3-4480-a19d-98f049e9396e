/**
 * HeroModern Theme - Complete React Application
 * 基于 HeroUI 设计的 v2board 前端应用
 * 参考 CyberPunk 主题的 API 实现
 */

(function() {
  'use strict';

  // 等待所有依赖加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initApp);
  } else {
    initApp();
  }

  function initApp() {
    console.log('🚀 HeroModern Theme with React Initialized');

    // 检查React依赖
    if (typeof React === 'undefined') {
      console.error('React not loaded, creating fallback app');
      createFallbackApp();
      return;
    }

    // 初始化主题
    initHeroModernTheme();

    // 渲染React应用
    const root = document.getElementById('root');
    if (root) {
      if (ReactDOM.createRoot) {
        const reactRoot = ReactDOM.createRoot(root);
        reactRoot.render(React.createElement(App));
      } else {
        ReactDOM.render(React.createElement(App), root);
      }
    }
  }

  function createFallbackApp() {
    // 如果React未加载，创建原生JavaScript应用
    const root = document.getElementById('root');
    if (root) {
      root.innerHTML = `
        <div class="hero-layout">
          <div class="hero-sidebar modern">
            <div class="hero-sidebar-header">
              <div class="hero-logo">H</div>
              <div class="hero-brand">HeroModern</div>
            </div>
            <nav class="hero-nav">
              <div class="hero-nav-item">
                <a href="#/dashboard" class="hero-nav-link active">
                  <span class="hero-nav-icon">📊</span>
                  <span class="hero-nav-text">仪表板</span>
                </a>
              </div>
              <div class="hero-nav-item">
                <a href="#/subscription" class="hero-nav-link">
                  <span class="hero-nav-icon">📋</span>
                  <span class="hero-nav-text">订阅</span>
                </a>
              </div>
              <div class="hero-nav-item">
                <a href="#/traffic" class="hero-nav-link">
                  <span class="hero-nav-icon">📈</span>
                  <span class="hero-nav-text">流量</span>
                </a>
              </div>
            </nav>
          </div>
          <div class="hero-main">
            <div class="hero-navbar floating">
              <div class="hero-navbar-left">
                <button class="hero-toggle-btn">☰</button>
                <h1>仪表板</h1>
              </div>
              <div class="hero-navbar-right">
                <button class="heroui-button heroui-button-ghost heroui-button-small">🌓</button>
                <div class="hero-user-menu">
                  <div class="hero-avatar">U</div>
                  <span>用户</span>
                </div>
              </div>
            </div>
            <div class="hero-content">
              <div class="demo-grid">
                <div class="heroui-card">
                  <div class="heroui-card-body">
                    <h3>欢迎使用 HeroModern</h3>
                    <p>主题正在加载中...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      `;
    }
  }

  function initHeroModernTheme() {
    // 添加主题类到body
    document.body.classList.add('heromodern-theme');

    // 设置主题变量
    if (window.settings && window.settings.theme) {
      const theme = window.settings.theme;

      // 应用主题色
      if (theme.color) {
        applyThemeColor(theme.color);
      }

      // 应用主题模式
      if (theme.mode) {
        applyThemeMode(theme.mode);
      }

      // 初始化动画
      if (theme.animations !== false) {
        initAnimations();
      }

      // 初始化毛玻璃效果
      if (theme.blur !== false) {
        initBlurEffects();
      }
    }
  }

  // React应用组件
  function App() {
    const [loading, setLoading] = React.useState(true);
    const [collapsed, setCollapsed] = React.useState(
      localStorage.getItem('hero-sidebar-collapsed') === 'true'
    );
    const [currentRoute, setCurrentRoute] = React.useState('dashboard');
    const [user, setUser] = React.useState(null);
    const [sidebarOpen, setSidebarOpen] = React.useState(false);

    // 初始化应用
    React.useEffect(() => {
      initializeApp();
    }, []);

    const initializeApp = async () => {
      try {
        // 检查认证状态
        if (!window.authManager.isAuthenticated()) {
          await window.authManager.checkAuthStatus();
        }

        // 如果已认证，加载用户信息
        if (window.authManager.isAuthenticated()) {
          await loadUserInfo();
        }

        setLoading(false);
      } catch (error) {
        console.error('App initialization failed:', error);
        setLoading(false);
      }
    };

    const loadUserInfo = async () => {
      try {
        const result = await window.HeroAPI.getUser();
        if (result.success) {
          setUser(result.data);
        }
      } catch (error) {
        console.error('Failed to load user info:', error);
      }
    };

    // 路由处理
    React.useEffect(() => {
      const handleHashChange = () => {
        const hash = window.location.hash.slice(1) || 'dashboard';
        const route = hash.replace('/', '');
        setCurrentRoute(route);
      };

      window.addEventListener('hashchange', handleHashChange);
      handleHashChange(); // 初始化路由

      return () => window.removeEventListener('hashchange', handleHashChange);
    }, []);

    // 响应式处理
    React.useEffect(() => {
      const handleResize = () => {
        if (window.innerWidth <= 768) {
          setCollapsed(false);
          setSidebarOpen(false);
        }
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    const toggleSidebar = () => {
      const isMobile = window.innerWidth <= 768;
      
      if (isMobile) {
        setSidebarOpen(!sidebarOpen);
      } else {
        const newCollapsed = !collapsed;
        setCollapsed(newCollapsed);
        localStorage.setItem('hero-sidebar-collapsed', newCollapsed);
      }
    };

    const closeSidebar = () => {
      setSidebarOpen(false);
    };

    const toggleTheme = () => {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('hero-theme', newTheme);
      
      if (window.hero && window.hero.notify) {
        hero.notify(`已切换到${newTheme === 'dark' ? '暗色' : '亮色'}模式`, 'success');
      }
    };

    const navigate = (path) => {
      window.location.hash = path;
    };

    if (loading) {
      return React.createElement(
        'div',
        {
          className: 'hero-loading',
          style: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
            background: 'var(--heroui-background)'
          }
        },
        React.createElement('div', { className: 'hero-spinner' }),
        React.createElement('span', { style: { marginLeft: '16px' } }, '加载中...')
      );
    }

    return React.createElement(AppLayout, {
      collapsed,
      setCollapsed,
      currentRoute,
      setCurrentRoute,
      user,
      sidebarOpen,
      setSidebarOpen,
      toggleSidebar,
      closeSidebar,
      toggleTheme,
      navigate
    });
  }

  // 应用布局组件
  function AppLayout({
    collapsed, currentRoute, user, sidebarOpen,
    toggleSidebar, closeSidebar, toggleTheme, navigate
  }) {
    const menuItems = [
      { key: 'dashboard', icon: '📊', label: '仪表板', path: '/dashboard' },
      { key: 'subscription', icon: '📋', label: '我的订阅', path: '/subscription' },
      { key: 'buy-subscription', icon: '🛒', label: '购买订阅', path: '/buy-subscription' },
      { key: 'traffic', icon: '📈', label: '流量统计', path: '/traffic' },
      { key: 'orders', icon: '📦', label: '我的订单', path: '/orders' },
      { key: 'invite', icon: '👥', label: '邀请好友', path: '/invite' },
      { key: 'tickets', icon: '🎫', label: '我的工单', path: '/tickets' },
      { key: 'knowledge', icon: '📚', label: '知识库', path: '/knowledge' },
      { key: 'profile', icon: '👤', label: '个人资料', path: '/profile' }
    ];

    const getPageTitle = () => {
      const item = menuItems.find(item => item.key === currentRoute);
      return item ? item.label : '仪表板';
    };

    return React.createElement(
      'div',
      { className: 'hero-layout' },
      // 侧边栏
      React.createElement(Sidebar, {
        collapsed,
        sidebarOpen,
        menuItems,
        currentRoute,
        navigate
      }),
      // 主内容区域
      React.createElement(
        'div',
        { className: 'hero-main' },
        // 顶部导航栏
        React.createElement(Navbar, {
          title: getPageTitle(),
          user,
          toggleSidebar,
          toggleTheme
        }),
        // 内容区域
        React.createElement(
          'div',
          { className: 'hero-content' },
          React.createElement(RouteContent, { currentRoute, user, navigate })
        )
      ),
      // 移动端遮罩
      sidebarOpen && React.createElement('div', {
        className: 'hero-overlay',
        onClick: closeSidebar
      })
    );
  }

  // 工具函数
  function applyThemeColor(color) {
    const colorMap = {
      blue: { primary: '#006FEE', secondary: '#338EF7' },
      purple: { primary: '#7C3AED', secondary: '#9353D3' },
      green: { primary: '#17C964', secondary: '#45D483' },
      orange: { primary: '#F5A524', secondary: '#F7B955' },
      pink: { primary: '#F31260', secondary: '#F54180' },
      slate: { primary: '#64748B', secondary: '#94A3B8' }
    };

    const colors = colorMap[color] || colorMap.blue;
    document.documentElement.style.setProperty('--heroui-primary', colors.primary);
    document.documentElement.style.setProperty('--heroui-secondary', colors.secondary);
  }

  function applyThemeMode(mode) {
    let isDark = false;
    
    if (mode === 'auto') {
      isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      // 监听系统主题变化
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
      });
    } else {
      isDark = mode === 'dark';
    }
    
    document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
  }

  function initAnimations() {
    // 页面加载动画
    const root = document.getElementById('root');
    if (root) {
      root.style.opacity = '0';
      root.style.transform = 'translateY(20px)';
      root.style.transition = 'all 0.6s ease';
      
      setTimeout(() => {
        root.style.opacity = '1';
        root.style.transform = 'translateY(0)';
      }, 100);
    }
  }

  function initBlurEffects() {
    // 为支持毛玻璃效果的元素添加类
    document.body.classList.add('hero-blur-enabled');
  }

  // 侧边栏组件
  function Sidebar({ collapsed, sidebarOpen, menuItems, currentRoute, navigate }) {
    const sidebarClass = [
      'hero-sidebar',
      'modern',
      collapsed ? 'collapsed' : '',
      sidebarOpen ? 'open' : ''
    ].filter(Boolean).join(' ');

    return React.createElement(
      'div',
      { className: sidebarClass },
      React.createElement(
        'div',
        { className: 'hero-sidebar-header' },
        React.createElement('div', { className: 'hero-logo' }, 'H'),
        React.createElement('div', { className: 'hero-brand' }, window.settings?.title || 'HeroModern')
      ),
      React.createElement(
        'nav',
        { className: 'hero-nav' },
        menuItems.map(item =>
          React.createElement(
            'div',
            { key: item.key, className: 'hero-nav-item' },
            React.createElement(
              'a',
              {
                href: `#${item.path}`,
                className: `hero-nav-link ${currentRoute === item.key ? 'active' : ''}`,
                onClick: (e) => {
                  e.preventDefault();
                  navigate(item.path);
                }
              },
              React.createElement('span', { className: 'hero-nav-icon' }, item.icon),
              React.createElement('span', { className: 'hero-nav-text' }, item.label)
            )
          )
        )
      )
    );
  }

  // 导航栏组件
  function Navbar({ title, user, toggleSidebar, toggleTheme }) {
    const navbarClass = [
      'hero-navbar',
      window.settings?.theme?.navbar_style || 'floating',
      window.settings?.theme?.blur ? 'hero-blur' : ''
    ].filter(Boolean).join(' ');

    return React.createElement(
      'div',
      { className: navbarClass },
      React.createElement(
        'div',
        { className: 'hero-navbar-left' },
        React.createElement(
          'button',
          {
            className: 'hero-toggle-btn',
            onClick: toggleSidebar
          },
          React.createElement('span', null, '☰')
        ),
        React.createElement('h1', { style: { margin: 0, fontSize: '18px', fontWeight: 600 } }, title)
      ),
      React.createElement(
        'div',
        { className: 'hero-navbar-right' },
        React.createElement(
          'button',
          {
            className: 'heroui-button heroui-button-ghost heroui-button-small hero-theme-toggle',
            onClick: toggleTheme
          },
          '🌓'
        ),
        React.createElement(UserMenu, { user })
      )
    );
  }

  // 用户菜单组件
  function UserMenu({ user }) {
    const [menuOpen, setMenuOpen] = React.useState(false);

    return React.createElement(
      'div',
      { className: 'hero-user-menu', onClick: () => setMenuOpen(!menuOpen) },
      React.createElement(
        'div',
        { className: 'hero-avatar' },
        user?.avatar ? React.createElement('img', { src: user.avatar, alt: 'Avatar' }) : 'U'
      ),
      React.createElement('span', null, user?.email?.split('@')[0] || '用户')
    );
  }

  // 路由内容组件
  function RouteContent({ currentRoute, user, navigate }) {
    const routeComponents = {
      dashboard: Dashboard,
      subscription: MySubscription,
      'buy-subscription': BuySubscription,
      traffic: TrafficLog,
      orders: MyOrders,
      invite: MyInvites,
      tickets: MyTickets,
      knowledge: Knowledge,
      profile: Profile
    };

    const Component = routeComponents[currentRoute] || Dashboard;

    return React.createElement(
      'div',
      { className: 'hero-animate-fade' },
      React.createElement(Component, { user, navigate })
    );
  }

  // 导出全局函数
  window.HeroModernApp = {
    applyThemeColor,
    applyThemeMode,
    initAnimations,
    initBlurEffects
  };

})();
