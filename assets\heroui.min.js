/**
 * HeroUI Simplified JavaScript Components
 * A lightweight implementation of HeroUI components
 */

(function(global) {
  'use strict';

  // HeroUI namespace
  const HeroUI = {
    version: '1.0.0',
    components: {},
    utils: {}
  };

  // Utility functions
  HeroUI.utils = {
    // Generate unique ID
    generateId: function(prefix = 'heroui') {
      return prefix + '-' + Math.random().toString(36).substr(2, 9);
    },

    // Add event listener with cleanup
    addEventListener: function(element, event, handler) {
      element.addEventListener(event, handler);
      return function() {
        element.removeEventListener(event, handler);
      };
    },

    // Debounce function
    debounce: function(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // Get element by selector
    $: function(selector, context = document) {
      return context.querySelector(selector);
    },

    // Get elements by selector
    $$: function(selector, context = document) {
      return Array.from(context.querySelectorAll(selector));
    }
  };

  // Button Component
  HeroUI.components.Button = class {
    constructor(element, options = {}) {
      this.element = element;
      this.options = {
        ripple: true,
        disabled: false,
        ...options
      };
      this.init();
    }

    init() {
      if (this.options.ripple) {
        this.addRippleEffect();
      }
      
      if (this.options.disabled) {
        this.disable();
      }
    }

    addRippleEffect() {
      this.element.addEventListener('click', (e) => {
        if (this.element.disabled) return;

        const ripple = document.createElement('span');
        const rect = this.element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          transform: scale(0);
          animation: heroui-ripple 0.6s ease-out;
          pointer-events: none;
        `;

        this.element.style.position = 'relative';
        this.element.style.overflow = 'hidden';
        this.element.appendChild(ripple);

        setTimeout(() => {
          if (ripple.parentNode) {
            ripple.parentNode.removeChild(ripple);
          }
        }, 600);
      });
    }

    disable() {
      this.element.disabled = true;
      this.element.classList.add('heroui-button-disabled');
    }

    enable() {
      this.element.disabled = false;
      this.element.classList.remove('heroui-button-disabled');
    }
  };

  // Modal Component
  HeroUI.components.Modal = class {
    constructor(element, options = {}) {
      this.element = element;
      this.options = {
        backdrop: true,
        keyboard: true,
        focus: true,
        ...options
      };
      this.isOpen = false;
      this.init();
    }

    init() {
      this.createBackdrop();
      this.bindEvents();
    }

    createBackdrop() {
      this.backdrop = document.createElement('div');
      this.backdrop.className = 'heroui-modal-backdrop';
      this.backdrop.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--heroui-overlay);
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      `;
    }

    bindEvents() {
      if (this.options.backdrop) {
        this.backdrop.addEventListener('click', () => this.close());
      }

      if (this.options.keyboard) {
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape' && this.isOpen) {
            this.close();
          }
        });
      }
    }

    open() {
      if (this.isOpen) return;

      document.body.appendChild(this.backdrop);
      document.body.appendChild(this.element);
      document.body.style.overflow = 'hidden';

      this.element.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.9);
        z-index: 1001;
        opacity: 0;
        transition: all 0.3s ease;
      `;

      // Trigger animations
      requestAnimationFrame(() => {
        this.backdrop.style.opacity = '1';
        this.backdrop.style.visibility = 'visible';
        this.element.style.opacity = '1';
        this.element.style.transform = 'translate(-50%, -50%) scale(1)';
      });

      this.isOpen = true;

      if (this.options.focus) {
        const focusable = this.element.querySelector('[autofocus], input, button, textarea, select');
        if (focusable) focusable.focus();
      }
    }

    close() {
      if (!this.isOpen) return;

      this.backdrop.style.opacity = '0';
      this.backdrop.style.visibility = 'hidden';
      this.element.style.opacity = '0';
      this.element.style.transform = 'translate(-50%, -50%) scale(0.9)';

      setTimeout(() => {
        if (this.backdrop.parentNode) {
          this.backdrop.parentNode.removeChild(this.backdrop);
        }
        if (this.element.parentNode) {
          this.element.parentNode.removeChild(this.element);
        }
        document.body.style.overflow = '';
      }, 300);

      this.isOpen = false;
    }
  };

  // Dropdown Component
  HeroUI.components.Dropdown = class {
    constructor(element, options = {}) {
      this.element = element;
      this.trigger = element.querySelector('[data-dropdown-trigger]');
      this.menu = element.querySelector('[data-dropdown-menu]');
      this.options = {
        placement: 'bottom-start',
        offset: 8,
        ...options
      };
      this.isOpen = false;
      this.init();
    }

    init() {
      if (!this.trigger || !this.menu) return;

      this.menu.style.cssText = `
        position: absolute;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.2s ease;
        z-index: 1000;
      `;

      this.bindEvents();
    }

    bindEvents() {
      this.trigger.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggle();
      });

      document.addEventListener('click', () => {
        if (this.isOpen) this.close();
      });

      this.menu.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    open() {
      if (this.isOpen) return;

      this.updatePosition();
      this.menu.style.opacity = '1';
      this.menu.style.visibility = 'visible';
      this.menu.style.transform = 'translateY(0)';
      this.isOpen = true;
    }

    close() {
      if (!this.isOpen) return;

      this.menu.style.opacity = '0';
      this.menu.style.visibility = 'hidden';
      this.menu.style.transform = 'translateY(-10px)';
      this.isOpen = false;
    }

    toggle() {
      this.isOpen ? this.close() : this.open();
    }

    updatePosition() {
      const triggerRect = this.trigger.getBoundingClientRect();
      const menuRect = this.menu.getBoundingClientRect();
      
      let top = triggerRect.bottom + this.options.offset;
      let left = triggerRect.left;

      // Adjust if menu goes off screen
      if (left + menuRect.width > window.innerWidth) {
        left = triggerRect.right - menuRect.width;
      }

      if (top + menuRect.height > window.innerHeight) {
        top = triggerRect.top - menuRect.height - this.options.offset;
      }

      this.menu.style.top = top + 'px';
      this.menu.style.left = left + 'px';
    }
  };

  // Toast Component
  HeroUI.components.Toast = class {
    static show(message, type = 'info', duration = 3000) {
      const toast = document.createElement('div');
      toast.className = `heroui-toast heroui-toast-${type}`;
      toast.textContent = message;

      const colors = {
        info: 'var(--heroui-primary)',
        success: 'var(--heroui-success)',
        warning: 'var(--heroui-warning)',
        error: 'var(--heroui-danger)'
      };

      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        background: ${colors[type] || colors.info};
        color: white;
        border-radius: var(--heroui-radius-medium);
        font-weight: 500;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
        box-shadow: var(--heroui-shadow-medium);
      `;

      document.body.appendChild(toast);

      // Show animation
      requestAnimationFrame(() => {
        toast.style.transform = 'translateX(0)';
      });

      // Auto hide
      setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
          }
        }, 300);
      }, duration);

      return toast;
    }
  };

  // Auto-initialize components
  function autoInit() {
    // Initialize buttons
    HeroUI.utils.$$('.heroui-button').forEach(button => {
      if (!button._heroui_button) {
        button._heroui_button = new HeroUI.components.Button(button);
      }
    });

    // Initialize dropdowns
    HeroUI.utils.$$('[data-dropdown]').forEach(dropdown => {
      if (!dropdown._heroui_dropdown) {
        dropdown._heroui_dropdown = new HeroUI.components.Dropdown(dropdown);
      }
    });
  }

  // Add ripple animation CSS
  const style = document.createElement('style');
  style.textContent = `
    @keyframes heroui-ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
    
    .heroui-button-disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  `;
  document.head.appendChild(style);

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', autoInit);
  } else {
    autoInit();
  }

  // Expose HeroUI globally
  global.HeroUI = HeroUI;

  // Convenience methods
  global.heroToast = HeroUI.components.Toast.show;

})(window);
