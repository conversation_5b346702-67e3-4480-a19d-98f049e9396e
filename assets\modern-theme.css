/* HeroModern Theme - Modern UI Styles */

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--heroui-content2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--heroui-content4);
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--heroui-primary);
}

/* 主布局容器 */
.hero-layout {
  display: flex;
  min-height: 100vh;
  background: var(--heroui-background);
}

/* 侧边栏样式 */
.hero-sidebar {
  width: 280px;
  background: var(--heroui-content1);
  border-right: 1px solid var(--heroui-divider);
  transition: all 0.3s ease;
  position: relative;
  z-index: 100;
}

.hero-sidebar.collapsed {
  width: 80px;
}

.hero-sidebar.minimal {
  background: transparent;
  border-right: none;
}

.hero-sidebar.classic {
  background: var(--heroui-content2);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 侧边栏头部 */
.hero-sidebar-header {
  padding: 20px;
  border-bottom: 1px solid var(--heroui-divider);
  display: flex;
  align-items: center;
  gap: 12px;
}

.hero-logo {
  width: 32px;
  height: 32px;
  border-radius: var(--heroui-radius-medium);
  background: var(--heroui-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.hero-brand {
  font-size: 18px;
  font-weight: 600;
  color: var(--heroui-foreground);
  transition: opacity 0.3s ease;
}

.hero-sidebar.collapsed .hero-brand {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* 导航菜单 */
.hero-nav {
  padding: 20px 0;
}

.hero-nav-item {
  margin: 4px 16px;
  border-radius: var(--heroui-radius-medium);
  overflow: hidden;
}

.hero-nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: var(--heroui-foreground);
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: var(--heroui-radius-medium);
}

.hero-nav-link:hover {
  background: var(--heroui-content2);
  color: var(--heroui-primary);
}

.hero-nav-link.active {
  background: var(--heroui-primary);
  color: white;
}

.hero-nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.hero-nav-text {
  transition: opacity 0.3s ease;
}

.hero-sidebar.collapsed .hero-nav-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* 主内容区域 */
.hero-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

/* 顶部导航栏 */
.hero-navbar {
  height: 64px;
  background: var(--heroui-content1);
  border-bottom: 1px solid var(--heroui-divider);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: relative;
  z-index: 50;
}

.hero-navbar.floating {
  margin: 16px 24px 0;
  border-radius: var(--heroui-radius-large);
  border: 1px solid var(--heroui-divider);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hero-navbar.transparent {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .hero-navbar.transparent {
  background: rgba(24, 24, 27, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 导航栏左侧 */
.hero-navbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.hero-toggle-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: var(--heroui-radius-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s ease;
}

.hero-toggle-btn:hover {
  background: var(--heroui-content2);
}

/* 导航栏右侧 */
.hero-navbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.hero-user-menu {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: var(--heroui-radius-medium);
  cursor: pointer;
  transition: background 0.2s ease;
}

.hero-user-menu:hover {
  background: var(--heroui-content2);
}

.hero-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--heroui-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
}

/* 内容区域 */
.hero-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.hero-navbar.floating + .hero-content {
  padding-top: 8px;
}

/* 卡片样式 */
.hero-card {
  background: var(--heroui-content1);
  border: 1px solid var(--heroui-divider);
  border-radius: var(--heroui-radius-large);
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.hero-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.hero-card.blur {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .hero-card.blur {
  background: rgba(24, 24, 27, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 按钮样式增强 */
.hero-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: var(--heroui-radius-medium);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.hero-btn-primary {
  background: var(--heroui-primary);
  color: white;
}

.hero-btn-primary:hover {
  background: var(--heroui-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 111, 238, 0.3);
}

.hero-btn-secondary {
  background: var(--heroui-content2);
  color: var(--heroui-foreground);
}

.hero-btn-secondary:hover {
  background: var(--heroui-content3);
}

/* 遮罩层 */
.hero-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 150;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

/* 网格布局 */
.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100vh;
    z-index: 200;
    transition: left 0.3s ease;
  }

  .hero-sidebar.open {
    left: 0;
  }

  .hero-main {
    width: 100%;
  }

  .hero-navbar {
    padding: 0 16px;
  }

  .hero-content {
    padding: 16px;
  }

  .hero-navbar.floating {
    margin: 8px 16px 0;
  }

  .demo-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero-animate-fade {
  animation: fadeIn 0.3s ease;
}

.hero-animate-slide {
  animation: slideIn 0.3s ease;
}

/* 工具类 */
.hero-text-primary {
  color: var(--heroui-primary);
}

.hero-text-secondary {
  color: var(--heroui-secondary);
}

.hero-text-muted {
  color: var(--heroui-content4);
}

.hero-bg-primary {
  background: var(--heroui-primary);
}

.hero-bg-secondary {
  background: var(--heroui-secondary);
}

.hero-shadow {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hero-shadow-lg {
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
}
