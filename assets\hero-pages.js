/**
 * HeroModern Theme - Page Components
 * 所有页面组件的实现
 */

(function() {
  'use strict';

  // 使用全局API服务
  const api = window.HeroAPI;

  // 工具函数
  const utils = {
    formatBytes: (bytes) => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    formatDate: (date) => {
      return new Date(date).toLocaleString('zh-CN');
    },

    formatCurrency: (amount) => {
      return `¥${parseFloat(amount).toFixed(2)}`;
    }
  };

  // 仪表板组件
  window.Dashboard = function({ user, navigate }) {
    const [stats, setStats] = React.useState({
      totalUsers: 0,
      activeSubscriptions: 0,
      totalRevenue: 0,
      todayTraffic: 0
    });
    const [notices, setNotices] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      loadDashboardData();
    }, []);

    const loadDashboardData = async () => {
      try {
        // 加载统计数据
        const statsResult = await api.getDashboardStats();
        if (statsResult.success) {
          setStats({
            totalUsers: statsResult.data.total_users || 0,
            activeSubscriptions: statsResult.data.active_subscriptions || 0,
            totalRevenue: statsResult.data.total_revenue || 0,
            todayTraffic: statsResult.data.today_traffic || 0
          });
        } else {
          // 使用模拟数据作为后备
          setStats({
            totalUsers: 1234,
            activeSubscriptions: 856,
            totalRevenue: 12345.67,
            todayTraffic: 2.5 * 1024 * 1024 * 1024
          });
        }

        // 加载公告
        const noticesResult = await api.getNotices();
        if (noticesResult.success && noticesResult.data) {
          setNotices(noticesResult.data);
        } else {
          // 使用模拟数据作为后备
          setNotices([
            {
              id: 1,
              title: '系统维护通知',
              content: '系统将于今晚23:00-01:00进行维护，期间可能影响服务使用。',
              created_at: new Date().toISOString()
            },
            {
              id: 2,
              title: '新套餐上线',
              content: '全新高速套餐现已上线，享受更快的网络体验！',
              created_at: new Date(Date.now() - 86400000).toISOString()
            }
          ]);
        }

        setLoading(false);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
        // 使用模拟数据作为后备
        setStats({
          totalUsers: 1234,
          activeSubscriptions: 856,
          totalRevenue: 12345.67,
          todayTraffic: 2.5 * 1024 * 1024 * 1024
        });
        setNotices([]);
        setLoading(false);
      }
    };

    if (loading) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement('div', { className: 'heroui-card-body' }, '加载中...')
      );
    }

    return React.createElement(
      'div',
      null,
      // 统计卡片
      React.createElement(
        'div',
        { className: 'demo-grid', style: { marginBottom: '24px' } },
        React.createElement(StatCard, {
          title: '总用户数',
          value: stats.totalUsers.toLocaleString(),
          extra: '+12% 较上月',
          color: 'var(--heroui-primary)'
        }),
        React.createElement(StatCard, {
          title: '活跃订阅',
          value: stats.activeSubscriptions.toLocaleString(),
          extra: '+8% 较上月',
          color: 'var(--heroui-success)'
        }),
        React.createElement(StatCard, {
          title: '总收入',
          value: utils.formatCurrency(stats.totalRevenue),
          extra: '+15% 较上月',
          color: 'var(--heroui-warning)'
        }),
        React.createElement(StatCard, {
          title: '今日流量',
          value: utils.formatBytes(stats.todayTraffic),
          extra: '实时统计',
          color: 'var(--heroui-secondary)'
        })
      ),

      // 快速操作
      React.createElement(
        'div',
        { className: 'heroui-card', style: { marginBottom: '24px' } },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '⚡ 快速操作')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          React.createElement(
            'div',
            { style: { display: 'flex', gap: '12px', flexWrap: 'wrap' } },
            React.createElement(
              'button',
              {
                className: 'heroui-button heroui-button-primary',
                onClick: () => navigate('/buy-subscription')
              },
              '购买订阅'
            ),
            React.createElement(
              'button',
              {
                className: 'heroui-button heroui-button-secondary',
                onClick: () => navigate('/traffic')
              },
              '查看流量'
            ),
            React.createElement(
              'button',
              {
                className: 'heroui-button heroui-button-success',
                onClick: () => navigate('/invite')
              },
              '邀请好友'
            ),
            React.createElement(
              'button',
              {
                className: 'heroui-button heroui-button-ghost',
                onClick: () => navigate('/tickets')
              },
              '提交工单'
            )
          )
        )
      ),

      // 公告列表
      React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '📢 最新公告')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          notices.length > 0 ? notices.map(notice =>
            React.createElement(
              'div',
              {
                key: notice.id,
                style: {
                  padding: '16px 0',
                  borderBottom: '1px solid var(--heroui-divider)'
                }
              },
              React.createElement(
                'h4',
                { style: { margin: '0 0 8px 0', color: 'var(--heroui-primary)' } },
                notice.title
              ),
              React.createElement(
                'p',
                { style: { margin: '0 0 8px 0', color: 'var(--heroui-foreground)' } },
                notice.content
              ),
              React.createElement(
                'small',
                { style: { color: 'var(--heroui-content4)' } },
                utils.formatDate(notice.created_at)
              )
            )
          ) : React.createElement(
            'p',
            { style: { color: 'var(--heroui-content4)' } },
            '暂无公告'
          )
        )
      )
    );
  };

  // 统计卡片组件
  function StatCard({ title, value, extra, color }) {
    return React.createElement(
      'div',
      { className: 'heroui-card' },
      React.createElement(
        'div',
        { className: 'heroui-card-body', style: { textAlign: 'center' } },
        React.createElement(
          'h3',
          { style: { margin: '0 0 12px 0', color: 'var(--heroui-content4)' } },
          title
        ),
        React.createElement(
          'div',
          {
            style: {
              fontSize: '32px',
              fontWeight: 'bold',
              color: color,
              marginBottom: '8px'
            }
          },
          value
        ),
        extra && React.createElement(
          'p',
          { style: { margin: 0, color: 'var(--heroui-content4)', fontSize: '14px' } },
          extra
        )
      )
    );
  }

  // 我的订阅组件
  window.MySubscription = function({ user, navigate }) {
    const [subscription, setSubscription] = React.useState(null);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      loadSubscriptionData();
    }, []);

    const loadSubscriptionData = async () => {
      try {
        const result = await api.getSubscription();
        if (result.success && result.data) {
          setSubscription(result.data);
        } else {
          // 使用模拟数据作为后备
          setSubscription({
            plan_name: '高级套餐',
            status: 'active',
            expired_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            total_traffic: 100 * 1024 * 1024 * 1024,
            used_traffic: 25 * 1024 * 1024 * 1024,
            reset_day: 15
          });
        }
        setLoading(false);
      } catch (error) {
        console.error('Failed to load subscription data:', error);
        setSubscription(null);
        setLoading(false);
      }
    };

    if (loading) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement('div', { className: 'heroui-card-body' }, '加载中...')
      );
    }

    if (!subscription) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement(
          'div',
          { className: 'heroui-card-body', style: { textAlign: 'center', padding: '40px' } },
          React.createElement('h3', null, '暂无有效订阅'),
          React.createElement('p', { style: { color: 'var(--heroui-content4)' } }, '您还没有购买任何套餐'),
          React.createElement(
            'button',
            {
              className: 'heroui-button heroui-button-primary',
              onClick: () => navigate('/buy-subscription')
            },
            '立即购买'
          )
        )
      );
    }

    const usagePercent = (subscription.used_traffic / subscription.total_traffic) * 100;

    return React.createElement(
      'div',
      null,
      React.createElement(
        'div',
        { className: 'heroui-card', style: { marginBottom: '24px' } },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '📋 当前订阅')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          React.createElement(
            'div',
            { style: { display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px' } },
            React.createElement(
              'div',
              null,
              React.createElement('label', { style: { color: 'var(--heroui-content4)' } }, '套餐名称'),
              React.createElement('div', { style: { fontSize: '18px', fontWeight: '600' } }, subscription.plan_name)
            ),
            React.createElement(
              'div',
              null,
              React.createElement('label', { style: { color: 'var(--heroui-content4)' } }, '状态'),
              React.createElement(
                'div',
                { className: `heroui-badge heroui-badge-${subscription.status === 'active' ? 'success' : 'danger'}` },
                subscription.status === 'active' ? '有效' : '已过期'
              )
            ),
            React.createElement(
              'div',
              null,
              React.createElement('label', { style: { color: 'var(--heroui-content4)' } }, '到期时间'),
              React.createElement('div', null, utils.formatDate(subscription.expired_at))
            ),
            React.createElement(
              'div',
              null,
              React.createElement('label', { style: { color: 'var(--heroui-content4)' } }, '重置日期'),
              React.createElement('div', null, `每月${subscription.reset_day}日`)
            )
          )
        )
      ),

      // 流量使用情况
      React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '📊 流量使用情况')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          React.createElement(
            'div',
            { style: { marginBottom: '16px' } },
            React.createElement(
              'div',
              { style: { display: 'flex', justifyContent: 'space-between', marginBottom: '8px' } },
              React.createElement('span', null, '已使用'),
              React.createElement('span', null, `${utils.formatBytes(subscription.used_traffic)} / ${utils.formatBytes(subscription.total_traffic)}`)
            ),
            React.createElement(
              'div',
              { className: 'heroui-progress' },
              React.createElement('div', {
                className: 'heroui-progress-bar',
                style: { width: `${usagePercent}%` }
              })
            ),
            React.createElement(
              'div',
              { style: { textAlign: 'center', marginTop: '8px', color: 'var(--heroui-content4)' } },
              `使用率: ${usagePercent.toFixed(1)}%`
            )
          )
        )
      )
    );
  };

  // 购买订阅组件
  window.BuySubscription = function({ user, navigate }) {
    const [plans, setPlans] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      loadPlans();
    }, []);

    const loadPlans = async () => {
      try {
        const result = await api.getPlans();
        if (result.success && result.data) {
          setPlans(result.data.map(plan => ({
            ...plan,
            traffic: plan.transfer_enable || plan.traffic,
            features: plan.features || ['高速稳定', '24小时客服', '多设备支持']
          })));
        } else {
          // 使用模拟数据作为后备
          setPlans([
            {
              id: 1,
              name: '基础套餐',
              price: 19.99,
              traffic: 50 * 1024 * 1024 * 1024,
              speed: '100Mbps',
              devices: 3,
              features: ['高速稳定', '24小时客服', '多设备支持']
            },
            {
              id: 2,
              name: '高级套餐',
              price: 39.99,
              traffic: 200 * 1024 * 1024 * 1024,
              speed: '500Mbps',
              devices: 5,
              features: ['超高速稳定', '24小时客服', '多设备支持', '专线加速'],
              popular: true
            },
            {
              id: 3,
              name: '企业套餐',
              price: 99.99,
              traffic: 1024 * 1024 * 1024 * 1024,
              speed: '1Gbps',
              devices: 10,
              features: ['极速稳定', '24小时客服', '多设备支持', '专线加速', '企业级服务']
            }
          ]);
        }
        setLoading(false);
      } catch (error) {
        console.error('Failed to load plans:', error);
        setLoading(false);
      }
    };

    const handlePurchase = async (plan) => {
      try {
        if (window.hero && window.hero.notify) {
          hero.notify(`正在创建订单: ${plan.name}...`, 'info');
        }

        // 使用新的API方法购买套餐
        const result = await api.purchasePlan(plan.id, plan.month_price ? 'month' : 'quarter', 'alipay');

        if (result.success) {
          if (window.hero && window.hero.notify) {
            hero.notify('订单创建成功，正在跳转到支付页面...', 'success');
          }
          // 跳转到支付页面
          if (result.data && result.data.payment_url) {
            window.location.href = result.data.payment_url;
          } else {
            // 如果没有支付URL，跳转到订单页面
            navigate('/orders');
          }
        } else {
          if (window.hero && window.hero.notify) {
            hero.notify(result.message || '订单创建失败', 'error');
          }
        }
      } catch (error) {
        console.error('Purchase error:', error);
        if (window.hero && window.hero.notify) {
          hero.notify('购买失败，请稍后重试', 'error');
        }
      }
    };

    if (loading) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement('div', { className: 'heroui-card-body' }, '加载套餐中...')
      );
    }

    return React.createElement(
      'div',
      null,
      React.createElement(
        'div',
        { style: { textAlign: 'center', marginBottom: '32px' } },
        React.createElement('h1', { style: { fontSize: '32px', marginBottom: '16px' } }, '选择适合您的套餐'),
        React.createElement('p', { style: { color: 'var(--heroui-content4)', fontSize: '16px' } }, '所有套餐均支持全平台使用，无限制访问')
      ),
      React.createElement(
        'div',
        { className: 'demo-grid' },
        plans.map(plan =>
          React.createElement(
            'div',
            {
              key: plan.id,
              className: 'heroui-card',
              style: {
                position: 'relative',
                border: plan.popular ? '2px solid var(--heroui-primary)' : undefined
              }
            },
            plan.popular && React.createElement(
              'div',
              {
                style: {
                  position: 'absolute',
                  top: '-12px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  background: 'var(--heroui-primary)',
                  color: 'white',
                  padding: '4px 16px',
                  borderRadius: 'var(--heroui-radius-medium)',
                  fontSize: '12px',
                  fontWeight: '600'
                }
              },
              '推荐'
            ),
            React.createElement(
              'div',
              { className: 'heroui-card-body', style: { textAlign: 'center' } },
              React.createElement('h3', { style: { marginBottom: '16px' } }, plan.name),
              React.createElement(
                'div',
                { style: { marginBottom: '24px' } },
                React.createElement(
                  'span',
                  { style: { fontSize: '36px', fontWeight: 'bold', color: 'var(--heroui-primary)' } },
                  `¥${plan.price}`
                ),
                React.createElement('span', { style: { color: 'var(--heroui-content4)' } }, '/月')
              ),
              React.createElement(
                'div',
                { style: { marginBottom: '24px', textAlign: 'left' } },
                React.createElement('div', { style: { marginBottom: '8px' } }, `📊 流量: ${utils.formatBytes(plan.traffic)}`),
                React.createElement('div', { style: { marginBottom: '8px' } }, `⚡ 速度: ${plan.speed}`),
                React.createElement('div', { style: { marginBottom: '8px' } }, `📱 设备: ${plan.devices}台`),
                React.createElement(
                  'div',
                  { style: { marginTop: '16px' } },
                  React.createElement('strong', null, '特色功能:'),
                  React.createElement(
                    'ul',
                    { style: { margin: '8px 0', paddingLeft: '20px' } },
                    plan.features.map((feature, index) =>
                      React.createElement('li', { key: index }, feature)
                    )
                  )
                )
              ),
              React.createElement(
                'button',
                {
                  className: `heroui-button ${plan.popular ? 'heroui-button-primary' : 'heroui-button-secondary'}`,
                  style: { width: '100%' },
                  onClick: () => handlePurchase(plan)
                },
                '立即购买'
              )
            )
          )
        )
      )
    );
  };

  // 流量统计组件
  window.TrafficLog = function({ user, navigate }) {
    const [trafficData, setTrafficData] = React.useState(null);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      loadTrafficData();
    }, []);

    const loadTrafficData = async () => {
      try {
        // 模拟API调用
        setTimeout(() => {
          setTrafficData({
            today: {
              upload: 1.2 * 1024 * 1024 * 1024,
              download: 3.8 * 1024 * 1024 * 1024
            },
            thisMonth: {
              upload: 15.6 * 1024 * 1024 * 1024,
              download: 45.2 * 1024 * 1024 * 1024
            },
            logs: [
              {
                id: 1,
                date: new Date().toISOString(),
                upload: 256 * 1024 * 1024,
                download: 1.2 * 1024 * 1024 * 1024,
                server: '香港节点01'
              },
              {
                id: 2,
                date: new Date(Date.now() - 3600000).toISOString(),
                upload: 128 * 1024 * 1024,
                download: 800 * 1024 * 1024,
                server: '新加坡节点02'
              }
            ]
          });
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Failed to load traffic data:', error);
        setLoading(false);
      }
    };

    if (loading) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement('div', { className: 'heroui-card-body' }, '加载流量数据中...')
      );
    }

    return React.createElement(
      'div',
      null,
      // 流量统计卡片
      React.createElement(
        'div',
        { className: 'demo-grid', style: { marginBottom: '24px' } },
        React.createElement(StatCard, {
          title: '今日上传',
          value: utils.formatBytes(trafficData.today.upload),
          color: 'var(--heroui-success)'
        }),
        React.createElement(StatCard, {
          title: '今日下载',
          value: utils.formatBytes(trafficData.today.download),
          color: 'var(--heroui-primary)'
        }),
        React.createElement(StatCard, {
          title: '本月上传',
          value: utils.formatBytes(trafficData.thisMonth.upload),
          color: 'var(--heroui-warning)'
        }),
        React.createElement(StatCard, {
          title: '本月下载',
          value: utils.formatBytes(trafficData.thisMonth.download),
          color: 'var(--heroui-secondary)'
        })
      ),

      // 流量记录
      React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '📈 流量记录')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          React.createElement(
            'div',
            { style: { overflowX: 'auto' } },
            React.createElement(
              'table',
              { style: { width: '100%', borderCollapse: 'collapse' } },
              React.createElement(
                'thead',
                null,
                React.createElement(
                  'tr',
                  { style: { borderBottom: '1px solid var(--heroui-divider)' } },
                  React.createElement('th', { style: { padding: '12px', textAlign: 'left' } }, '时间'),
                  React.createElement('th', { style: { padding: '12px', textAlign: 'left' } }, '服务器'),
                  React.createElement('th', { style: { padding: '12px', textAlign: 'right' } }, '上传'),
                  React.createElement('th', { style: { padding: '12px', textAlign: 'right' } }, '下载')
                )
              ),
              React.createElement(
                'tbody',
                null,
                trafficData.logs.map(log =>
                  React.createElement(
                    'tr',
                    { key: log.id, style: { borderBottom: '1px solid var(--heroui-divider)' } },
                    React.createElement('td', { style: { padding: '12px' } }, utils.formatDate(log.date)),
                    React.createElement('td', { style: { padding: '12px' } }, log.server),
                    React.createElement('td', { style: { padding: '12px', textAlign: 'right' } }, utils.formatBytes(log.upload)),
                    React.createElement('td', { style: { padding: '12px', textAlign: 'right' } }, utils.formatBytes(log.download))
                  )
                )
              )
            )
          )
        )
      )
    );
  };

  // 我的订单组件
  window.MyOrders = function({ user, navigate }) {
    const [orders, setOrders] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      loadOrders();
    }, []);

    const loadOrders = async () => {
      try {
        const result = await api.getOrders();
        if (result.success && result.data) {
          setOrders(result.data);
        } else {
          // 使用模拟数据作为后备
          setOrders([
            {
              trade_no: 'ORD001',
              plan_name: '高级套餐',
              total_amount: 39.99,
              status: 2, // 已支付
              created_at: new Date().toISOString(),
              expired_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
              trade_no: 'ORD002',
              plan_name: '基础套餐',
              total_amount: 19.99,
              status: 0, // 待支付
              created_at: new Date(Date.now() - 86400000).toISOString(),
              expired_at: null
            }
          ]);
        }
        setLoading(false);
      } catch (error) {
        console.error('Failed to load orders:', error);
        setLoading(false);
      }
    };

    const getStatusBadge = (status) => {
      // v2board 订单状态：0=待支付, 1=开通中, 2=已取消, 3=已完成, 4=已折抵
      const statusMap = {
        0: { text: '待支付', class: 'heroui-badge-warning' },
        1: { text: '开通中', class: 'heroui-badge-primary' },
        2: { text: '已取消', class: 'heroui-badge-danger' },
        3: { text: '已完成', class: 'heroui-badge-success' },
        4: { text: '已折抵', class: 'heroui-badge-secondary' }
      };
      const statusInfo = statusMap[status] || { text: '未知', class: 'heroui-badge-secondary' };

      return React.createElement(
        'span',
        { className: `heroui-badge ${statusInfo.class}` },
        statusInfo.text
      );
    };

    if (loading) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement('div', { className: 'heroui-card-body' }, '加载订单中...')
      );
    }

    return React.createElement(
      'div',
      { className: 'heroui-card' },
      React.createElement(
        'div',
        { className: 'heroui-card-header' },
        React.createElement('h3', null, '📦 我的订单')
      ),
      React.createElement(
        'div',
        { className: 'heroui-card-body' },
        orders.length > 0 ? orders.map(order =>
          React.createElement(
            'div',
            {
              key: order.trade_no || order.id,
              style: {
                padding: '16px',
                border: '1px solid var(--heroui-divider)',
                borderRadius: 'var(--heroui-radius-medium)',
                marginBottom: '16px'
              }
            },
            React.createElement(
              'div',
              { style: { display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' } },
              React.createElement('strong', null, `订单号: ${order.trade_no || order.id}`),
              getStatusBadge(order.status)
            ),
            React.createElement(
              'div',
              { style: { display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '12px' } },
              React.createElement(
                'div',
                null,
                React.createElement('label', { style: { color: 'var(--heroui-content4)' } }, '套餐名称'),
                React.createElement('div', null, order.plan_name || order.plan?.name || '未知套餐')
              ),
              React.createElement(
                'div',
                null,
                React.createElement('label', { style: { color: 'var(--heroui-content4)' } }, '金额'),
                React.createElement('div', null, utils.formatCurrency(order.total_amount || order.amount))
              ),
              React.createElement(
                'div',
                null,
                React.createElement('label', { style: { color: 'var(--heroui-content4)' } }, '创建时间'),
                React.createElement('div', null, utils.formatDate(order.created_at))
              ),
              (order.expired_at || order.period) && React.createElement(
                'div',
                null,
                React.createElement('label', { style: { color: 'var(--heroui-content4)' } }, '周期'),
                React.createElement('div', null, order.period || utils.formatDate(order.expired_at))
              )
            )
          )
        ) : React.createElement(
          'div',
          { style: { textAlign: 'center', padding: '40px', color: 'var(--heroui-content4)' } },
          '暂无订单记录'
        )
      )
    );
  };

  // 邀请好友组件
  window.MyInvites = function({ user, navigate }) {
    const [inviteData, setInviteData] = React.useState(null);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
      loadInviteData();
    }, []);

    const loadInviteData = async () => {
      try {
        setTimeout(() => {
          setInviteData({
            invite_code: 'HERO2025',
            invite_url: 'https://example.com/register?code=HERO2025',
            commission_rate: 0.3,
            total_commission: 156.78,
            invite_count: 12,
            invites: [
              {
                id: 1,
                email: '<EMAIL>',
                commission: 11.97,
                status: 'active',
                created_at: new Date(Date.now() - 86400000).toISOString()
              },
              {
                id: 2,
                email: '<EMAIL>',
                commission: 23.94,
                status: 'active',
                created_at: new Date(Date.now() - 2 * 86400000).toISOString()
              }
            ]
          });
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Failed to load invite data:', error);
        setLoading(false);
      }
    };

    const copyInviteCode = () => {
      navigator.clipboard.writeText(inviteData.invite_code).then(() => {
        if (window.hero && window.hero.notify) {
          hero.notify('邀请码已复制到剪贴板', 'success');
        }
      });
    };

    const copyInviteUrl = () => {
      navigator.clipboard.writeText(inviteData.invite_url).then(() => {
        if (window.hero && window.hero.notify) {
          hero.notify('邀请链接已复制到剪贴板', 'success');
        }
      });
    };

    if (loading) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement('div', { className: 'heroui-card-body' }, '加载邀请数据中...')
      );
    }

    return React.createElement(
      'div',
      null,
      // 邀请统计
      React.createElement(
        'div',
        { className: 'demo-grid', style: { marginBottom: '24px' } },
        React.createElement(StatCard, {
          title: '邀请人数',
          value: inviteData.invite_count.toString(),
          color: 'var(--heroui-primary)'
        }),
        React.createElement(StatCard, {
          title: '佣金比例',
          value: `${(inviteData.commission_rate * 100)}%`,
          color: 'var(--heroui-success)'
        }),
        React.createElement(StatCard, {
          title: '总佣金',
          value: utils.formatCurrency(inviteData.total_commission),
          color: 'var(--heroui-warning)'
        })
      ),

      // 邀请工具
      React.createElement(
        'div',
        { className: 'heroui-card', style: { marginBottom: '24px' } },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '👥 邀请工具')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          React.createElement(
            'div',
            { style: { marginBottom: '16px' } },
            React.createElement('label', { style: { display: 'block', marginBottom: '8px' } }, '邀请码'),
            React.createElement(
              'div',
              { style: { display: 'flex', gap: '8px' } },
              React.createElement('input', {
                type: 'text',
                className: 'heroui-input',
                value: inviteData.invite_code,
                readOnly: true,
                style: { flex: 1 }
              }),
              React.createElement(
                'button',
                {
                  className: 'heroui-button heroui-button-secondary',
                  onClick: copyInviteCode
                },
                '复制'
              )
            )
          ),
          React.createElement(
            'div',
            null,
            React.createElement('label', { style: { display: 'block', marginBottom: '8px' } }, '邀请链接'),
            React.createElement(
              'div',
              { style: { display: 'flex', gap: '8px' } },
              React.createElement('input', {
                type: 'text',
                className: 'heroui-input',
                value: inviteData.invite_url,
                readOnly: true,
                style: { flex: 1 }
              }),
              React.createElement(
                'button',
                {
                  className: 'heroui-button heroui-button-secondary',
                  onClick: copyInviteUrl
                },
                '复制'
              )
            )
          )
        )
      ),

      // 邀请记录
      React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '📋 邀请记录')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          inviteData.invites.length > 0 ? inviteData.invites.map(invite =>
            React.createElement(
              'div',
              {
                key: invite.id,
                style: {
                  padding: '16px 0',
                  borderBottom: '1px solid var(--heroui-divider)',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }
              },
              React.createElement(
                'div',
                null,
                React.createElement('div', { style: { fontWeight: '600' } }, invite.email),
                React.createElement('div', { style: { color: 'var(--heroui-content4)', fontSize: '14px' } }, utils.formatDate(invite.created_at))
              ),
              React.createElement(
                'div',
                { style: { textAlign: 'right' } },
                React.createElement('div', { style: { color: 'var(--heroui-success)', fontWeight: '600' } }, utils.formatCurrency(invite.commission)),
                React.createElement(
                  'span',
                  { className: `heroui-badge heroui-badge-${invite.status === 'active' ? 'success' : 'secondary'}` },
                  invite.status === 'active' ? '有效' : '无效'
                )
              )
            )
          ) : React.createElement(
            'div',
            { style: { textAlign: 'center', padding: '40px', color: 'var(--heroui-content4)' } },
            '暂无邀请记录'
          )
        )
      )
    );
  };

  // 工单系统组件
  window.MyTickets = function({ user, navigate }) {
    const [tickets, setTickets] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [showCreateForm, setShowCreateForm] = React.useState(false);

    React.useEffect(() => {
      loadTickets();
    }, []);

    const loadTickets = async () => {
      try {
        setTimeout(() => {
          setTickets([
            {
              id: 'TK001',
              subject: '连接问题咨询',
              status: 'open',
              priority: 'medium',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              replies: 2
            },
            {
              id: 'TK002',
              subject: '账单问题',
              status: 'closed',
              priority: 'low',
              created_at: new Date(Date.now() - 2 * 86400000).toISOString(),
              updated_at: new Date(Date.now() - 86400000).toISOString(),
              replies: 5
            }
          ]);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Failed to load tickets:', error);
        setLoading(false);
      }
    };

    const getStatusBadge = (status) => {
      const statusMap = {
        open: { text: '开启', class: 'heroui-badge-success' },
        pending: { text: '待处理', class: 'heroui-badge-warning' },
        closed: { text: '已关闭', class: 'heroui-badge-secondary' }
      };
      const statusInfo = statusMap[status] || { text: '未知', class: 'heroui-badge-secondary' };

      return React.createElement(
        'span',
        { className: `heroui-badge ${statusInfo.class}` },
        statusInfo.text
      );
    };

    const getPriorityBadge = (priority) => {
      const priorityMap = {
        low: { text: '低', class: 'heroui-badge-secondary' },
        medium: { text: '中', class: 'heroui-badge-warning' },
        high: { text: '高', class: 'heroui-badge-danger' }
      };
      const priorityInfo = priorityMap[priority] || { text: '未知', class: 'heroui-badge-secondary' };

      return React.createElement(
        'span',
        { className: `heroui-badge ${priorityInfo.class}` },
        priorityInfo.text
      );
    };

    if (loading) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement('div', { className: 'heroui-card-body' }, '加载工单中...')
      );
    }

    return React.createElement(
      'div',
      null,
      // 创建工单按钮
      React.createElement(
        'div',
        { style: { marginBottom: '24px', textAlign: 'right' } },
        React.createElement(
          'button',
          {
            className: 'heroui-button heroui-button-primary',
            onClick: () => setShowCreateForm(!showCreateForm)
          },
          showCreateForm ? '取消创建' : '创建工单'
        )
      ),

      // 创建工单表单
      showCreateForm && React.createElement(CreateTicketForm, {
        onSubmit: (ticketData) => {
          console.log('Creating ticket:', ticketData);
          setShowCreateForm(false);
          if (window.hero && window.hero.notify) {
            hero.notify('工单创建成功', 'success');
          }
        },
        onCancel: () => setShowCreateForm(false)
      }),

      // 工单列表
      React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '🎫 我的工单')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          tickets.length > 0 ? tickets.map(ticket =>
            React.createElement(
              'div',
              {
                key: ticket.id,
                style: {
                  padding: '16px',
                  border: '1px solid var(--heroui-divider)',
                  borderRadius: 'var(--heroui-radius-medium)',
                  marginBottom: '16px'
                }
              },
              React.createElement(
                'div',
                { style: { display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' } },
                React.createElement('strong', null, `#${ticket.id} ${ticket.subject}`),
                React.createElement(
                  'div',
                  { style: { display: 'flex', gap: '8px' } },
                  getStatusBadge(ticket.status),
                  getPriorityBadge(ticket.priority)
                )
              ),
              React.createElement(
                'div',
                { style: { display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '12px', color: 'var(--heroui-content4)', fontSize: '14px' } },
                React.createElement('div', null, `创建时间: ${utils.formatDate(ticket.created_at)}`),
                React.createElement('div', null, `更新时间: ${utils.formatDate(ticket.updated_at)}`),
                React.createElement('div', null, `回复数: ${ticket.replies}`)
              )
            )
          ) : React.createElement(
            'div',
            { style: { textAlign: 'center', padding: '40px', color: 'var(--heroui-content4)' } },
            '暂无工单记录'
          )
        )
      )
    );
  };

  // 创建工单表单组件
  function CreateTicketForm({ onSubmit, onCancel }) {
    const [formData, setFormData] = React.useState({
      subject: '',
      priority: 'medium',
      content: ''
    });

    const handleSubmit = (e) => {
      e.preventDefault();
      if (!formData.subject.trim() || !formData.content.trim()) {
        if (window.hero && window.hero.notify) {
          hero.notify('请填写完整信息', 'warning');
        }
        return;
      }
      onSubmit(formData);
    };

    return React.createElement(
      'div',
      { className: 'heroui-card', style: { marginBottom: '24px' } },
      React.createElement(
        'div',
        { className: 'heroui-card-header' },
        React.createElement('h3', null, '创建新工单')
      ),
      React.createElement(
        'div',
        { className: 'heroui-card-body' },
        React.createElement(
          'form',
          { onSubmit: handleSubmit },
          React.createElement(
            'div',
            { style: { marginBottom: '16px' } },
            React.createElement('label', { style: { display: 'block', marginBottom: '8px' } }, '主题'),
            React.createElement('input', {
              type: 'text',
              className: 'heroui-input',
              value: formData.subject,
              onChange: (e) => setFormData({ ...formData, subject: e.target.value }),
              placeholder: '请输入工单主题'
            })
          ),
          React.createElement(
            'div',
            { style: { marginBottom: '16px' } },
            React.createElement('label', { style: { display: 'block', marginBottom: '8px' } }, '优先级'),
            React.createElement('select', {
              className: 'heroui-input',
              value: formData.priority,
              onChange: (e) => setFormData({ ...formData, priority: e.target.value })
            },
              React.createElement('option', { value: 'low' }, '低'),
              React.createElement('option', { value: 'medium' }, '中'),
              React.createElement('option', { value: 'high' }, '高')
            )
          ),
          React.createElement(
            'div',
            { style: { marginBottom: '16px' } },
            React.createElement('label', { style: { display: 'block', marginBottom: '8px' } }, '内容'),
            React.createElement('textarea', {
              className: 'heroui-input',
              value: formData.content,
              onChange: (e) => setFormData({ ...formData, content: e.target.value }),
              placeholder: '请详细描述您遇到的问题',
              rows: 5,
              style: { resize: 'vertical' }
            })
          ),
          React.createElement(
            'div',
            { style: { display: 'flex', gap: '12px' } },
            React.createElement(
              'button',
              { type: 'submit', className: 'heroui-button heroui-button-primary' },
              '提交工单'
            ),
            React.createElement(
              'button',
              { type: 'button', className: 'heroui-button heroui-button-secondary', onClick: onCancel },
              '取消'
            )
          )
        )
      )
    );
  }

  // 知识库组件
  window.Knowledge = function({ user, navigate }) {
    const [articles, setArticles] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [searchTerm, setSearchTerm] = React.useState('');

    React.useEffect(() => {
      loadArticles();
    }, []);

    const loadArticles = async () => {
      try {
        setTimeout(() => {
          setArticles([
            {
              id: 1,
              title: '如何配置客户端',
              category: '使用教程',
              content: '详细介绍各平台客户端的配置方法...',
              views: 1234,
              helpful: 89,
              created_at: new Date().toISOString()
            },
            {
              id: 2,
              title: '常见连接问题解决方案',
              category: '故障排除',
              content: '针对常见的连接问题提供解决方案...',
              views: 856,
              helpful: 67,
              created_at: new Date(Date.now() - 86400000).toISOString()
            },
            {
              id: 3,
              title: '套餐选择指南',
              category: '购买指南',
              content: '帮助您选择最适合的套餐...',
              views: 567,
              helpful: 45,
              created_at: new Date(Date.now() - 2 * 86400000).toISOString()
            }
          ]);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Failed to load articles:', error);
        setLoading(false);
      }
    };

    const filteredArticles = articles.filter(article =>
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.category.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (loading) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement('div', { className: 'heroui-card-body' }, '加载知识库中...')
      );
    }

    return React.createElement(
      'div',
      null,
      // 搜索框
      React.createElement(
        'div',
        { className: 'heroui-card', style: { marginBottom: '24px' } },
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          React.createElement('input', {
            type: 'text',
            className: 'heroui-input',
            placeholder: '搜索文章...',
            value: searchTerm,
            onChange: (e) => setSearchTerm(e.target.value)
          })
        )
      ),

      // 文章列表
      React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '📚 知识库文章')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          filteredArticles.length > 0 ? filteredArticles.map(article =>
            React.createElement(
              'div',
              {
                key: article.id,
                style: {
                  padding: '16px 0',
                  borderBottom: '1px solid var(--heroui-divider)',
                  cursor: 'pointer'
                },
                onClick: () => {
                  if (window.hero && window.hero.notify) {
                    hero.notify(`正在查看文章: ${article.title}`, 'info');
                  }
                }
              },
              React.createElement(
                'div',
                { style: { display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' } },
                React.createElement('h4', { style: { margin: 0, color: 'var(--heroui-primary)' } }, article.title),
                React.createElement(
                  'span',
                  { className: 'heroui-badge heroui-badge-secondary' },
                  article.category
                )
              ),
              React.createElement('p', { style: { margin: '8px 0', color: 'var(--heroui-content4)' } }, article.content),
              React.createElement(
                'div',
                { style: { display: 'flex', gap: '16px', fontSize: '14px', color: 'var(--heroui-content4)' } },
                React.createElement('span', null, `👀 ${article.views} 次查看`),
                React.createElement('span', null, `👍 ${article.helpful} 人觉得有用`),
                React.createElement('span', null, utils.formatDate(article.created_at))
              )
            )
          ) : React.createElement(
            'div',
            { style: { textAlign: 'center', padding: '40px', color: 'var(--heroui-content4)' } },
            searchTerm ? '未找到相关文章' : '暂无文章'
          )
        )
      )
    );
  };

  // 个人资料组件
  window.Profile = function({ user, navigate }) {
    const [profile, setProfile] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [editing, setEditing] = React.useState(false);

    React.useEffect(() => {
      loadProfile();
    }, []);

    const loadProfile = async () => {
      try {
        setTimeout(() => {
          setProfile({
            email: '<EMAIL>',
            username: 'herouser',
            avatar: null,
            phone: '+86 138****8888',
            balance: 99.99,
            commission: 15.50,
            created_at: new Date(Date.now() - 30 * 86400000).toISOString(),
            two_factor_enabled: false
          });
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Failed to load profile:', error);
        setLoading(false);
      }
    };

    const handleSave = () => {
      setEditing(false);
      if (window.hero && window.hero.notify) {
        hero.notify('个人资料已更新', 'success');
      }
    };

    if (loading) {
      return React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement('div', { className: 'heroui-card-body' }, '加载个人资料中...')
      );
    }

    return React.createElement(
      'div',
      null,
      // 基本信息
      React.createElement(
        'div',
        { className: 'heroui-card', style: { marginBottom: '24px' } },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement(
            'div',
            { style: { display: 'flex', justifyContent: 'space-between', alignItems: 'center' } },
            React.createElement('h3', null, '👤 基本信息'),
            React.createElement(
              'button',
              {
                className: 'heroui-button heroui-button-secondary heroui-button-small',
                onClick: () => editing ? handleSave() : setEditing(true)
              },
              editing ? '保存' : '编辑'
            )
          )
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          React.createElement(
            'div',
            { style: { display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' } },
            React.createElement(
              'div',
              null,
              React.createElement('label', { style: { display: 'block', marginBottom: '8px' } }, '邮箱'),
              editing ? React.createElement('input', {
                type: 'email',
                className: 'heroui-input',
                value: profile.email,
                onChange: (e) => setProfile({ ...profile, email: e.target.value })
              }) : React.createElement('div', null, profile.email)
            ),
            React.createElement(
              'div',
              null,
              React.createElement('label', { style: { display: 'block', marginBottom: '8px' } }, '用户名'),
              editing ? React.createElement('input', {
                type: 'text',
                className: 'heroui-input',
                value: profile.username,
                onChange: (e) => setProfile({ ...profile, username: e.target.value })
              }) : React.createElement('div', null, profile.username)
            ),
            React.createElement(
              'div',
              null,
              React.createElement('label', { style: { display: 'block', marginBottom: '8px' } }, '手机号'),
              editing ? React.createElement('input', {
                type: 'tel',
                className: 'heroui-input',
                value: profile.phone,
                onChange: (e) => setProfile({ ...profile, phone: e.target.value })
              }) : React.createElement('div', null, profile.phone)
            ),
            React.createElement(
              'div',
              null,
              React.createElement('label', { style: { display: 'block', marginBottom: '8px' } }, '注册时间'),
              React.createElement('div', null, utils.formatDate(profile.created_at))
            )
          )
        )
      ),

      // 账户信息
      React.createElement(
        'div',
        { className: 'demo-grid', style: { marginBottom: '24px' } },
        React.createElement(StatCard, {
          title: '账户余额',
          value: utils.formatCurrency(profile.balance),
          color: 'var(--heroui-primary)'
        }),
        React.createElement(StatCard, {
          title: '推广佣金',
          value: utils.formatCurrency(profile.commission),
          color: 'var(--heroui-success)'
        })
      ),

      // 安全设置
      React.createElement(
        'div',
        { className: 'heroui-card' },
        React.createElement(
          'div',
          { className: 'heroui-card-header' },
          React.createElement('h3', null, '🔒 安全设置')
        ),
        React.createElement(
          'div',
          { className: 'heroui-card-body' },
          React.createElement(
            'div',
            { style: { display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' } },
            React.createElement(
              'div',
              null,
              React.createElement('strong', null, '两步验证'),
              React.createElement('p', { style: { margin: '4px 0 0 0', color: 'var(--heroui-content4)' } }, '为您的账户添加额外的安全保护')
            ),
            React.createElement(
              'button',
              {
                className: `heroui-button ${profile.two_factor_enabled ? 'heroui-button-danger' : 'heroui-button-success'} heroui-button-small`,
                onClick: () => {
                  setProfile({ ...profile, two_factor_enabled: !profile.two_factor_enabled });
                  if (window.hero && window.hero.notify) {
                    hero.notify(`两步验证已${!profile.two_factor_enabled ? '启用' : '禁用'}`, 'success');
                  }
                }
              },
              profile.two_factor_enabled ? '禁用' : '启用'
            )
          ),
          React.createElement(
            'div',
            { style: { display: 'flex', justifyContent: 'space-between', alignItems: 'center' } },
            React.createElement(
              'div',
              null,
              React.createElement('strong', null, '修改密码'),
              React.createElement('p', { style: { margin: '4px 0 0 0', color: 'var(--heroui-content4)' } }, '定期更换密码以保护账户安全')
            ),
            React.createElement(
              'button',
              {
                className: 'heroui-button heroui-button-secondary heroui-button-small',
                onClick: () => {
                  if (window.hero && window.hero.notify) {
                    hero.notify('密码修改功能正在开发中', 'info');
                  }
                }
              },
              '修改密码'
            )
          )
        )
      )
    );
  };

})();
