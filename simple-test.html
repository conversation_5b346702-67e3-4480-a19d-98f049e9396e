<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>HeroModern Simple Test</title>
  
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: #f8f9fa;
    }
    
    .test-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 6px;
      font-weight: 500;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    .test-button {
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      padding: 8px 16px;
      margin: 5px;
      cursor: pointer;
    }
    
    .test-button:hover {
      background: #0056b3;
    }
    
    #react-app {
      margin: 20px 0;
      padding: 20px;
      border: 2px dashed #dee2e6;
      border-radius: 8px;
      text-align: center;
    }
  </style>
</head>

<body>
  <div class="test-container">
    <h1>🧪 HeroModern 简单测试</h1>
    <p>测试React加载和基本功能</p>
    
    <div id="status-container">
      <div class="status info">正在检查依赖...</div>
    </div>
    
    <div>
      <button class="test-button" onclick="testReact()">测试React</button>
      <button class="test-button" onclick="testAPI()">测试API</button>
      <button class="test-button" onclick="testTheme()">测试主题</button>
    </div>
    
    <div id="react-app">
      <p>React应用将在这里渲染</p>
    </div>
  </div>

  <!-- 按正确顺序加载脚本 -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

  <script>
    // 状态管理
    function addStatus(message, type = 'info') {
      const container = document.getElementById('status-container');
      const status = document.createElement('div');
      status.className = `status ${type}`;
      status.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      container.appendChild(status);
    }

    // 检查依赖
    function checkDependencies() {
      console.log('Checking dependencies...');
      
      if (typeof React !== 'undefined') {
        addStatus('✅ React 加载成功', 'success');
        console.log('React version:', React.version);
      } else {
        addStatus('❌ React 加载失败', 'error');
        return false;
      }
      
      if (typeof ReactDOM !== 'undefined') {
        addStatus('✅ ReactDOM 加载成功', 'success');
      } else {
        addStatus('❌ ReactDOM 加载失败', 'error');
        return false;
      }
      
      return true;
    }

    // 测试React渲染
    function testReact() {
      if (!checkDependencies()) {
        addStatus('❌ 依赖检查失败，无法测试React', 'error');
        return;
      }

      try {
        // 创建简单的React组件
        const TestComponent = React.createElement(
          'div',
          { style: { padding: '20px', background: '#e3f2fd', borderRadius: '8px' } },
          React.createElement('h3', null, '🎉 React 渲染成功！'),
          React.createElement('p', null, `React 版本: ${React.version}`),
          React.createElement('p', null, `当前时间: ${new Date().toLocaleString()}`)
        );

        // 渲染到页面
        const container = document.getElementById('react-app');
        if (ReactDOM.createRoot) {
          const root = ReactDOM.createRoot(container);
          root.render(TestComponent);
          addStatus('✅ React 18 createRoot 渲染成功', 'success');
        } else {
          ReactDOM.render(TestComponent, container);
          addStatus('✅ React 传统渲染成功', 'success');
        }
      } catch (error) {
        addStatus(`❌ React 渲染失败: ${error.message}`, 'error');
        console.error('React render error:', error);
      }
    }

    // 测试API
    function testAPI() {
      addStatus('🔍 开始测试API...', 'info');
      
      // 模拟API调用
      fetch('/api/v1/passport/comm/config')
        .then(response => {
          if (response.ok) {
            addStatus('✅ API 连接成功', 'success');
            return response.json();
          } else {
            throw new Error(`HTTP ${response.status}`);
          }
        })
        .then(data => {
          addStatus(`✅ API 响应: ${JSON.stringify(data).substring(0, 100)}...`, 'success');
        })
        .catch(error => {
          addStatus(`⚠️ API 测试失败: ${error.message}`, 'error');
          console.log('API test failed (expected in test environment):', error);
        });
    }

    // 测试主题
    function testTheme() {
      addStatus('🎨 测试主题功能...', 'info');
      
      try {
        // 切换主题
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        document.documentElement.setAttribute('data-theme', newTheme);
        
        addStatus(`✅ 主题切换到: ${newTheme}`, 'success');
        
        // 2秒后切换回来
        setTimeout(() => {
          document.documentElement.setAttribute('data-theme', currentTheme || 'light');
          addStatus(`🔄 主题恢复到: ${currentTheme || 'light'}`, 'info');
        }, 2000);
        
      } catch (error) {
        addStatus(`❌ 主题测试失败: ${error.message}`, 'error');
      }
    }

    // 页面加载完成后自动检查
    document.addEventListener('DOMContentLoaded', () => {
      addStatus('📄 DOM 加载完成', 'success');
      
      // 延迟检查，确保React完全加载
      setTimeout(() => {
        if (checkDependencies()) {
          addStatus('🚀 所有依赖加载完成，可以开始测试', 'success');
          
          // 自动测试React渲染
          setTimeout(testReact, 500);
        } else {
          addStatus('❌ 依赖加载失败，请检查网络连接', 'error');
        }
      }, 100);
    });

    // 全局错误处理
    window.addEventListener('error', (event) => {
      addStatus(`❌ 全局错误: ${event.error.message}`, 'error');
      console.error('Global error:', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
      addStatus(`❌ 未处理的Promise错误: ${event.reason}`, 'error');
      console.error('Unhandled promise rejection:', event.reason);
    });

    addStatus('🚀 测试脚本加载完成', 'info');
  </script>
</body>
</html>
