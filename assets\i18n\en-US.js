// English language pack
window.i18n = window.i18n || {};
window.i18n['en-US'] = {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    reset: 'Reset',
    refresh: 'Refresh',
    copy: 'Copy',
    download: 'Download',
    upload: 'Upload',
    view: 'View',
    settings: 'Settings',
    help: 'Help',
    logout: 'Logout',
    login: 'Login',
    register: 'Register'
  },

  // Navigation
  nav: {
    dashboard: 'Dashboard',
    subscription: 'Subscription',
    traffic: 'Traffic',
    invite: 'Invite',
    profile: 'Profile',
    billing: 'Billing',
    ticket: 'Ticket',
    knowledge: 'Knowledge',
    admin: 'Admin'
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    welcome: 'Welcome back',
    overview: 'Overview',
    quickActions: 'Quick Actions',
    recentActivity: 'Recent Activity',
    statistics: 'Statistics',
    announcements: 'Announcements',
    systemStatus: 'System Status'
  },

  // Subscription
  subscription: {
    title: 'My Subscription',
    current: 'Current Subscription',
    expired: 'Expired',
    active: 'Active',
    inactive: 'Inactive',
    renew: 'Renew',
    upgrade: 'Upgrade',
    downgrade: 'Downgrade',
    cancel: 'Cancel Subscription',
    details: 'Subscription Details',
    history: 'Subscription History',
    plans: 'Plans'
  },

  // Traffic
  traffic: {
    title: 'Traffic Statistics',
    used: 'Used',
    remaining: 'Remaining',
    total: 'Total',
    upload: 'Upload',
    download: 'Download',
    today: 'Today',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    reset: 'Reset Time',
    unlimited: 'Unlimited'
  },

  // Invite
  invite: {
    title: 'Invite Friends',
    code: 'Invite Code',
    link: 'Invite Link',
    commission: 'Commission',
    invitees: 'Invitees',
    earnings: 'Earnings',
    generate: 'Generate Code',
    share: 'Share Link',
    history: 'Invite History'
  },

  // Profile
  profile: {
    title: 'Profile',
    avatar: 'Avatar',
    username: 'Username',
    email: 'Email',
    phone: 'Phone',
    password: 'Password',
    changePassword: 'Change Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    updateProfile: 'Update Profile',
    twoFactor: 'Two-Factor Authentication',
    enable: 'Enable',
    disable: 'Disable'
  },

  // Billing
  billing: {
    title: 'Billing',
    balance: 'Balance',
    recharge: 'Recharge',
    withdraw: 'Withdraw',
    history: 'Transaction History',
    invoice: 'Invoice',
    payment: 'Payment',
    amount: 'Amount',
    method: 'Payment Method',
    status: 'Status',
    date: 'Date',
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed'
  },

  // Ticket
  ticket: {
    title: 'Ticket System',
    create: 'Create Ticket',
    subject: 'Subject',
    priority: 'Priority',
    status: 'Status',
    category: 'Category',
    description: 'Description',
    attachment: 'Attachment',
    reply: 'Reply',
    close: 'Close Ticket',
    open: 'Open',
    closed: 'Closed',
    pending: 'Pending',
    resolved: 'Resolved',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent'
  },

  // Knowledge
  knowledge: {
    title: 'Knowledge Base',
    search: 'Search Articles',
    categories: 'Categories',
    popular: 'Popular Articles',
    recent: 'Recent Articles',
    helpful: 'Helpful',
    notHelpful: 'Not Helpful',
    feedback: 'Feedback'
  },

  // Form validation
  validation: {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    password: 'Password must be at least 8 characters',
    confirmPassword: 'Passwords do not match',
    phone: 'Please enter a valid phone number',
    url: 'Please enter a valid URL',
    number: 'Please enter a valid number',
    min: 'Minimum value is {min}',
    max: 'Maximum value is {max}',
    minLength: 'Minimum length is {min} characters',
    maxLength: 'Maximum length is {max} characters'
  },

  // Messages
  message: {
    saveSuccess: 'Saved successfully',
    saveFailed: 'Save failed',
    deleteSuccess: 'Deleted successfully',
    deleteFailed: 'Delete failed',
    updateSuccess: 'Updated successfully',
    updateFailed: 'Update failed',
    uploadSuccess: 'Uploaded successfully',
    uploadFailed: 'Upload failed',
    copySuccess: 'Copied successfully',
    copyFailed: 'Copy failed',
    networkError: 'Network error, please try again later',
    serverError: 'Server error',
    unauthorized: 'Unauthorized access',
    forbidden: 'Access forbidden',
    notFound: 'Page not found',
    timeout: 'Request timeout'
  },

  // Time
  time: {
    now: 'Just now',
    minutesAgo: '{n} minutes ago',
    hoursAgo: '{n} hours ago',
    daysAgo: '{n} days ago',
    weeksAgo: '{n} weeks ago',
    monthsAgo: '{n} months ago',
    yearsAgo: '{n} years ago',
    second: 'second',
    minute: 'minute',
    hour: 'hour',
    day: 'day',
    week: 'week',
    month: 'month',
    year: 'year'
  }
};
