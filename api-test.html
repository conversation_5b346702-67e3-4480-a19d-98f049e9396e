<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HeroModern API Test</title>
  <link rel="stylesheet" href="assets/heroui.min.css">
  <link rel="stylesheet" href="assets/modern-theme.css">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: var(--heroui-background);
      color: var(--heroui-foreground);
    }
    
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .test-section {
      margin-bottom: 40px;
    }
    
    .test-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
    }
    
    .test-button {
      margin: 8px;
    }
    
    .test-result {
      background: var(--heroui-content1);
      border: 1px solid var(--heroui-divider);
      border-radius: var(--heroui-radius-medium);
      padding: 16px;
      margin-top: 16px;
      max-height: 300px;
      overflow-y: auto;
    }
    
    .test-result pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-size: 12px;
    }
    
    .success {
      color: var(--heroui-success);
    }
    
    .error {
      color: var(--heroui-danger);
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>HeroModern API 测试</h1>
    
    <!-- 认证相关API -->
    <div class="test-section">
      <div class="heroui-card">
        <div class="heroui-card-header">
          <h2>🔐 认证相关 API</h2>
        </div>
        <div class="heroui-card-body">
          <button class="heroui-button heroui-button-primary test-button" onclick="testAPI('getConfig')">
            获取配置
          </button>
          <button class="heroui-button heroui-button-secondary test-button" onclick="testAPI('checkAuth')">
            检查认证
          </button>
          <button class="heroui-button heroui-button-success test-button" onclick="testLogin()">
            测试登录
          </button>
          <button class="heroui-button heroui-button-warning test-button" onclick="testAPI('sendEmailVerify', '<EMAIL>')">
            发送验证码
          </button>
          <div id="auth-result" class="test-result" style="display: none;"></div>
        </div>
      </div>
    </div>

    <!-- 用户相关API -->
    <div class="test-section">
      <div class="heroui-card">
        <div class="heroui-card-header">
          <h2>👤 用户相关 API</h2>
        </div>
        <div class="heroui-card-body">
          <button class="heroui-button heroui-button-primary test-button" onclick="testAPI('getUser')">
            获取用户信息
          </button>
          <button class="heroui-button heroui-button-secondary test-button" onclick="testAPI('getSubscription')">
            获取订阅信息
          </button>
          <button class="heroui-button heroui-button-success test-button" onclick="testAPI('getDashboardStats')">
            获取统计数据
          </button>
          <button class="heroui-button heroui-button-warning test-button" onclick="testAPI('resetSubscribeUrl')">
            重置订阅链接
          </button>
          <div id="user-result" class="test-result" style="display: none;"></div>
        </div>
      </div>
    </div>

    <!-- 套餐和订单API -->
    <div class="test-section">
      <div class="test-grid">
        <div class="heroui-card">
          <div class="heroui-card-header">
            <h3>📋 套餐相关</h3>
          </div>
          <div class="heroui-card-body">
            <button class="heroui-button heroui-button-primary test-button" onclick="testAPI('getPlans')">
              获取套餐列表
            </button>
            <div id="plan-result" class="test-result" style="display: none;"></div>
          </div>
        </div>

        <div class="heroui-card">
          <div class="heroui-card-header">
            <h3>📦 订单相关</h3>
          </div>
          <div class="heroui-card-body">
            <button class="heroui-button heroui-button-primary test-button" onclick="testAPI('getOrders')">
              获取订单列表
            </button>
            <button class="heroui-button heroui-button-secondary test-button" onclick="testAPI('getPaymentMethods')">
              获取支付方式
            </button>
            <div id="order-result" class="test-result" style="display: none;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务器和流量API -->
    <div class="test-section">
      <div class="test-grid">
        <div class="heroui-card">
          <div class="heroui-card-header">
            <h3>🖥️ 服务器相关</h3>
          </div>
          <div class="heroui-card-body">
            <button class="heroui-button heroui-button-primary test-button" onclick="testAPI('getServers')">
              获取服务器列表
            </button>
            <button class="heroui-button heroui-button-secondary test-button" onclick="testAPI('getTrafficLog')">
              获取流量记录
            </button>
            <div id="server-result" class="test-result" style="display: none;"></div>
          </div>
        </div>

        <div class="heroui-card">
          <div class="heroui-card-header">
            <h3>👥 邀请相关</h3>
          </div>
          <div class="heroui-card-body">
            <button class="heroui-button heroui-button-primary test-button" onclick="testAPI('getInviteData')">
              获取邀请信息
            </button>
            <button class="heroui-button heroui-button-secondary test-button" onclick="testAPI('generateInviteCode')">
              生成邀请码
            </button>
            <div id="invite-result" class="test-result" style="display: none;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工单和知识库API -->
    <div class="test-section">
      <div class="test-grid">
        <div class="heroui-card">
          <div class="heroui-card-header">
            <h3>🎫 工单相关</h3>
          </div>
          <div class="heroui-card-body">
            <button class="heroui-button heroui-button-primary test-button" onclick="testAPI('getTickets')">
              获取工单列表
            </button>
            <button class="heroui-button heroui-button-secondary test-button" onclick="testCreateTicket()">
              创建测试工单
            </button>
            <div id="ticket-result" class="test-result" style="display: none;"></div>
          </div>
        </div>

        <div class="heroui-card">
          <div class="heroui-card-header">
            <h3>📚 知识库相关</h3>
          </div>
          <div class="heroui-card-body">
            <button class="heroui-button heroui-button-primary test-button" onclick="testAPI('getKnowledgeArticles')">
              获取知识库文章
            </button>
            <button class="heroui-button heroui-button-secondary test-button" onclick="testAPI('getNotices')">
              获取公告
            </button>
            <div id="knowledge-result" class="test-result" style="display: none;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全局结果显示 -->
    <div class="test-section">
      <div class="heroui-card">
        <div class="heroui-card-header">
          <h2>📊 测试结果</h2>
        </div>
        <div class="heroui-card-body">
          <div id="global-result" class="test-result"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 加载脚本 -->
  <script src="assets/heroui.min.js"></script>
  <script src="assets/modern-theme.js"></script>
  <script src="assets/hero-api.js"></script>

  <script>
    // 测试API函数
    async function testAPI(method, ...args) {
      const resultContainer = document.getElementById('global-result');
      
      try {
        resultContainer.innerHTML = `<div style="color: var(--heroui-primary);">正在测试 ${method}...</div>`;
        
        const result = await window.HeroAPI[method](...args);
        
        const resultHtml = `
          <div class="success">✅ ${method} 测试成功</div>
          <pre>${JSON.stringify(result, null, 2)}</pre>
        `;
        
        resultContainer.innerHTML = resultHtml;
        
        // 同时显示在对应的区域
        const sectionMap = {
          getConfig: 'auth-result',
          checkAuth: 'auth-result',
          sendEmailVerify: 'auth-result',
          getUser: 'user-result',
          getSubscription: 'user-result',
          getDashboardStats: 'user-result',
          resetSubscribeUrl: 'user-result',
          getPlans: 'plan-result',
          getOrders: 'order-result',
          getPaymentMethods: 'order-result',
          getServers: 'server-result',
          getTrafficLog: 'server-result',
          getInviteData: 'invite-result',
          generateInviteCode: 'invite-result',
          getTickets: 'ticket-result',
          getKnowledgeArticles: 'knowledge-result',
          getNotices: 'knowledge-result'
        };
        
        const sectionResult = document.getElementById(sectionMap[method]);
        if (sectionResult) {
          sectionResult.style.display = 'block';
          sectionResult.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
        }
        
      } catch (error) {
        const errorHtml = `
          <div class="error">❌ ${method} 测试失败</div>
          <pre>${error.message}</pre>
        `;
        
        resultContainer.innerHTML = errorHtml;
        console.error(`API Test ${method} failed:`, error);
      }
    }

    // 测试登录
    async function testLogin() {
      const credentials = {
        email: '<EMAIL>',
        password: 'testpassword'
      };
      
      await testAPI('login', credentials);
    }

    // 测试创建工单
    async function testCreateTicket() {
      const ticketData = {
        subject: 'API测试工单',
        level: 1,
        message: '这是一个API测试工单'
      };
      
      await testAPI('createTicket', ticketData);
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🚀 HeroModern API Test Page Loaded');
      
      // 显示API可用性
      const globalResult = document.getElementById('global-result');
      globalResult.innerHTML = `
        <div style="color: var(--heroui-success);">
          ✅ HeroAPI 已加载，可以开始测试
        </div>
        <div style="margin-top: 8px; color: var(--heroui-content4);">
          点击上方按钮测试各个API接口
        </div>
      `;
    });
  </script>
</body>
</html>
