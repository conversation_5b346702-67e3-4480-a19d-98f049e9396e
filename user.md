## 1.登出 失效

> `GET` /user/logout

- 请求参数
  `null`

- 成功返回示例 `json`

```json
{
  "data": true
}
```

| 参数名  | 类型      | 描述     |
|------|---------|--------|
| data | boolean | 是否登出成功 |

## 2.账号信息

> `GET` /api/v1/user/info

- 请求参数
  `null`

- 成功返回示例 `json`

```json
{
    "status": "success",
    "message": "操作成功",
    "data": {
        "email": "<EMAIL>",
        "transfer_enable": ************,
        "last_login_at": 1749559435,
        "created_at": 1740222921,
        "banned": 0,
        "remind_expire": true,
        "remind_traffic": true,
        "expired_at": 1771763102,
        "balance": 0,
        "commission_balance": 0,
        "plan_id": 5,
        "discount": null,
        "commission_rate": null,
        "telegram_id": null,
        "uuid": "6af88184-8390-45e5-8d56-af28fb6c2439",
        "avatar_url": "https://cdn.v2ex.com/gravatar/454141dab8fba2c55bc2d81247a629a4?s=64&d=identicon"
    },
    "error": null
}
```

| 参数名                | 类型                     | 描述      |
|--------------------|------------------------|---------|
| email              | string                 | 邮箱地址    |
| transfer_enable    | number                 | 总可用流量   |
| last_login_at      | timestamp              | 最后登入时间  |
| created_at         | timestamp              | 创建时间    |
| banned             | number                 | 是否封禁使用  |
| remind_expire      | number                 | 到期邮件提醒  |
| remind_traffic     | number                 | 流量邮件提醒  |
| expired_at         | timestamp              | 过期时间    |
| balance            | number                 | 用户余额    |
| commission_balance | number                 | 佣金余额    |
| plan_id            | number - object(&plan) | 当前订阅id  |
| discount           | number                 | 消费折扣    |
| commission_rate    | number                 | 佣金率     |
| telegram_id        | number                 | 绑定TG id |
| uuid               | string                 | 唯一UUID  |
| avatar_url         | string                 | 头像地址    |

## 3.订阅信息

> `GET` /api/v1/user/getSubscribe

- 请求参数
  `null`

- 成功返回示例 `json`

```json
{
    "status": "success",
    "message": "操作成功",
    "data": {
        "plan_id": 5,
        "token": "********************************",
        "expired_at": 1771763102,
        "u": 1564556698,
        "d": 70019403566,
        "transfer_enable": ************,
        "email": "<EMAIL>",
        "uuid": "6af88184-8390-45e5-8d56-af28fb6c2439",
        "plan": {
            "id": 5,
            "group_id": 1,
            "transfer_enable": 600,
            "name": "至尊套餐-600G",
            "speed_limit": null,
            "show": true,
            "sort": 5,
            "renew": true,
            "content": "<style>\n    .no-wrap {\n        white-space:pre-wrap;;\n    }\n</style>\n\n<div class=\"no-wrap\">\n✅更快的网络\n✅流媒体解锁\n✅享有IPLC游戏专线\n✅不限制设备数\n✅600 GB 流量/月\n✅1000 Mpbs 限速\n✅在线客服售后保障\n</div>",
            "reset_traffic_method": null,
            "capacity_limit": null,
            "created_at": 1740226680,
            "updated_at": 1740242155,
            "prices": {
                "monthly": "100",
                "quarterly": null,
                "half_yearly": null,
                "yearly": null,
                "two_yearly": null,
                "three_yearly": null,
                "onetime": null,
                "reset_traffic": null
            },
            "sell": 1,
            "device_limit": null
        },
        "subscribe_url": "http://fastly.fast-ai.xyz/s/********************************",
        "reset_day": 12
    },
    "error": null
}
```

| 参数名             | 类型                     | 描述       |
|-----------------|------------------------|----------|
| plan_id         | number - object(&plan) | 订阅id     |
| token           | string                 | 用户 token |
| expired_at      | timestamp              | 过期时间     |
| u               | number                 | 已用上行流量   |
| d               | number                 | 已用下行流量   |
| transfer_enable | number                 | 总可用流量    |
| email           | string                 | 邮箱地址     |
| subscribe_url   | string                 | 订阅链接     |
| reset_day       | number                 | 重置日      |

## 4.重置订阅链接

> `GET` /api/v1/user/resetSecurity

- 请求参数
  `null`

- 成功返回示例 `json`

```json
{
    "status": "success",
    "message": "操作成功",
    "data": "http://fastly.fast-ai.xyz/s/********************************",
    "error": null
}
```

| 参数名  | 类型     | 描述   |
|------|--------|------|
| data | string | 订阅链接 |

## 5.代办事项

> `GET` /api/v1/user/getStat

- 请求参数
  `null`

- 成功返回示例 `json`

```json
{
    "status": "success",
    "message": "操作成功",
    "data": [
        0,
        0,
        0
    ],
    "error": null
}
```

| 参数名     | 类型     | 描述    |
|---------|--------|-------|
| data[0] | number | 待付订单  |
| data[1] | number | 代办工单  |
| data[2] | number | 待确认邀请 |

## 6.修改密码

> `POST` /api/v1/user/changePassword

- 请求参数 `json`

```json
old_password
new_password
```

| 参数名          | 类型     | 必填  | 描述  |
|--------------|--------|-----|-----|
| old_password | string | ✔︎  | 旧密码 |
| new_password | string | ✔︎  | 新密码 |

- 成功返回示例 `json`

```json
{
    "status": "success",
    "message": "操作成功",
    "data": true,
    "error": null
}
```

| 参数名  | 类型      | 描述     |
|------|---------|--------|
| data | boolean | 是否修改成功 |

## 7.通知状态

> `POST` /api/v1/user/user/update

- 请求参数 `json`

```json
{
  "remind_expire": 0
}
```

| 参数名            | 类型     | 必填  | 描述     |
|----------------|--------|-----|--------|
| remind_expire  | number | ✖︎︎ | 到期邮件提醒 |
| remind_traffic | number | ✖︎  | 流量邮件提醒 |

remind_expire=0 关闭
remind_expire=1 开启
- 成功返回示例 `json`

```json
{
    "status": "success",
    "message": "操作成功",
    "data": true,
    "error": null
}
```

| 参数名  | 类型      | 描述     |
|------|---------|--------|
| data | boolean | 是否修改成功 |

## 8.佣金划转 失效

> `POST` /user/transfer

- 请求参数 `json`

```json
{
  "transfer_amount": 1000
}
```

| 参数名             | 类型     | 必填  | 描述   |
|-----------------|--------|-----|------|
| transfer_amount | number | ✔︎  | 划转金额 |

- 成功返回示例 `json`

```json
{
  "data": true
}
```

| 参数名  | 类型      | 描述     |
|------|---------|--------|
| data | boolean | 是否划转成功 |