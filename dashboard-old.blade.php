<!DOCTYPE html>
<html>

<head>
  <!-- HeroModern Theme CSS -->
  <link rel="stylesheet" href="/theme/{{$theme}}/assets/components.chunk.css?v={{$version}}">
  <link rel="stylesheet" href="/theme/{{$theme}}/assets/umi.css?v={{$version}}">
  @if (file_exists(public_path("/theme/{$theme}/assets/custom.css")))
    <link rel="stylesheet" href="/theme/{{$theme}}/assets/custom.css?v={{$version}}">
  @endif

  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">

  @php ($colors = [
    'blue' => '#006FEE',
    'purple' => '#7C3AED',
    'green' => '#17C964',
    'orange' => '#F5A524',
    'pink' => '#F31260',
    'slate' => '#64748B'
  ])
  <meta name="theme-color" content="{{$colors[$theme_config['color'] ?? 'blue']}}">

  <title>{{$title}}</title>
  <script>window.routerBase = "/";</script>

  @php ($colors = [
      'blue' => '#006FEE',
      'purple' => '#7C3AED',
      'green' => '#17C964',
      'orange' => '#F5A524',
      'pink' => '#F31260',
      'slate' => '#64748B'
  ])
  <meta name="theme-color" content="{{$colors[$theme_config['theme_color']] ?? '#006FEE'}}">

  <!-- Preload critical resources -->
  <link rel="preload" href="/theme/{{$theme}}/assets/heroui.min.js" as="script">
  <link rel="preload" href="/theme/{{$theme}}/assets/modern-theme.js?v={{$version}}" as="script">

  <style>
    :root {
      /* HeroUI 主题变量 */
      --heroui-primary: #006FEE;
      --heroui-secondary: #9353D3;
      --heroui-success: #17C964;
      --heroui-warning: #F5A524;
      --heroui-danger: #F31260;
      --heroui-foreground: #11181C;
      --heroui-background: #FFFFFF;
      --heroui-content1: #FFFFFF;
      --heroui-content2: #F4F4F5;
      --heroui-content3: #E4E4E7;
      --heroui-content4: #D4D4D8;
      --heroui-default: #D4D4D8;
      --heroui-divider: #E4E4E7;
      --heroui-focus: #006FEE;
      --heroui-overlay: rgba(0, 0, 0, 0.5);
      --heroui-radius-small: 8px;
      --heroui-radius-medium: 12px;
      --heroui-radius-large: 16px;
    }

    /* 主题色配置 */
    @if($theme_config['theme_color'] === 'blue')
      :root {
        --heroui-primary: #006FEE;
        --heroui-secondary: #338EF7;
      }
    @elseif($theme_config['theme_color'] === 'purple')
      :root {
        --heroui-primary: #7C3AED;
        --heroui-secondary: #9353D3;
      }
    @elseif($theme_config['theme_color'] === 'green')
      :root {
        --heroui-primary: #17C964;
        --heroui-secondary: #45D483;
      }
    @elseif($theme_config['theme_color'] === 'orange')
      :root {
        --heroui-primary: #F5A524;
        --heroui-secondary: #F7B955;
      }
    @elseif($theme_config['theme_color'] === 'pink')
      :root {
        --heroui-primary: #F31260;
        --heroui-secondary: #F54180;
      }
    @elseif($theme_config['theme_color'] === 'slate')
      :root {
        --heroui-primary: #64748B;
        --heroui-secondary: #94A3B8;
      }
    @endif

    /* 暗色模式变量 */
    [data-theme="dark"] {
      --heroui-foreground: #ECEDEE;
      --heroui-background: #000000;
      --heroui-content1: #18181B;
      --heroui-content2: #27272A;
      --heroui-content3: #3F3F46;
      --heroui-content4: #52525B;
      --heroui-default: #3F3F46;
      --heroui-divider: #27272A;
      --heroui-overlay: rgba(0, 0, 0, 0.8);
    }

    /* 圆角风格配置 */
    @if($theme_config['border_radius'] === 'small')
      :root {
        --heroui-radius-small: 4px;
        --heroui-radius-medium: 6px;
        --heroui-radius-large: 8px;
      }
    @elseif($theme_config['border_radius'] === 'large')
      :root {
        --heroui-radius-small: 12px;
        --heroui-radius-medium: 16px;
        --heroui-radius-large: 20px;
      }
    @elseif($theme_config['border_radius'] === 'none')
      :root {
        --heroui-radius-small: 0px;
        --heroui-radius-medium: 0px;
        --heroui-radius-large: 0px;
      }
    @endif

    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      background: var(--heroui-background);
      color: var(--heroui-foreground);
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* 背景配置 */
    @if(!empty($theme_config['background_url']))
      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('{{$theme_config['background_url']}}') center/cover;
        opacity: 0.1;
        z-index: -2;
      }
    @else
      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--heroui-primary) 0%, var(--heroui-secondary) 100%);
        opacity: 0.05;
        z-index: -2;
      }
    @endif

    /* 毛玻璃效果 */
    @if($theme_config['enable_blur'] === 'true')
      .hero-blur {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
      }
    @endif

    /* 主应用容器 */
    #root {
      position: relative;
      z-index: 1;
      min-height: 100vh;
    }

    /* 禁用动画选项 */
    @if($theme_config['enable_animations'] === 'false')
      *, *::before, *::after {
        animation-duration: 0s !important;
        animation-delay: 0s !important;
        transition-duration: 0s !important;
        transition-delay: 0s !important;
      }
    @endif

    /* 加载动画 */
    .hero-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--heroui-background);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.3s ease;
    }

    .hero-loading.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .hero-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--heroui-content3);
      border-top: 3px solid var(--heroui-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>

<body>
  <!-- 加载动画 -->
  <div id="hero-loading" class="hero-loading">
    <div class="hero-spinner"></div>
  </div>

  <script>
    window.routerBase = "/";
    window.settings = {
      title: '{{$title}}',
      assets_path: '/theme/{{$theme}}/assets',
      theme: {
        mode: '{{ $theme_config['theme_mode'] ?? "auto" }}',
        color: '{{ $theme_config['theme_color'] ?? "blue" }}',
        sidebar_style: '{{ $theme_config['sidebar_style'] ?? "modern" }}',
        navbar_style: '{{ $theme_config['navbar_style'] ?? "floating" }}',
        animations: {{ $theme_config['enable_animations'] ?? 'true' }},
        blur: {{ $theme_config['enable_blur'] ?? 'true' }},
        border_radius: '{{ $theme_config['border_radius'] ?? 'medium' }}'
      },
      version: '{{$version}}',
      background_url: '{{$theme_config['background_url']}}',
      description: '{{$description}}',
      i18n: [
        'zh-CN',
        'en-US',
        'ja-JP',
        'vi-VN',
        'ko-KR',
        'zh-TW',
        'fa-IR'
      ],
      logo: '{{$logo}}',
      api: {
        base_url: '/api/v1',
        timeout: 30000
      }
    }

    // 主题模式检测和设置
    function initThemeMode() {
      const themeMode = window.settings.theme.mode;
      let isDark = false;
      
      if (themeMode === 'auto') {
        isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      } else {
        isDark = themeMode === 'dark';
      }
      
      document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
      
      // 监听系统主题变化
      if (themeMode === 'auto') {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
          document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        });
      }
    }

    // 初始化主题
    initThemeMode();

    // 隐藏加载动画
    window.addEventListener('load', () => {
      setTimeout(() => {
        const loading = document.getElementById('hero-loading');
        if (loading) {
          loading.classList.add('hidden');
          setTimeout(() => loading.remove(), 300);
        }
      }, 500);
    });
  </script>

  <!-- 应用根节点 -->
  <div id="root"></div>

  <!-- 国际化文件 -->
  <script src="/theme/{{$theme}}/assets/i18n/zh-CN.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/zh-TW.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/en-US.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/ja-JP.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/ko-KR.js?v={{$version}}"></script>

  <!-- 加载脚本 -->
  <script src="/theme/{{$theme}}/assets/heroui.min.js"></script>
  <script src="/theme/{{$theme}}/assets/modern-theme.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/hero-api.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/hero-auth.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/hero-pages.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/hero-app.js?v={{$version}}"></script>

  <!-- 自定义HTML -->
  {!! $theme_config['custom_html'] !!}
</body>

</html>
