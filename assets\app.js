/**
 * HeroModern Theme - Complete React Application
 * 基于 HeroUI 设计的 v2board 前端应用
 * 参考 CyberPunk 主题的 API 实现
 */

(function() {
  'use strict';

  // 等待React和其他依赖加载完成
  if (typeof React === 'undefined' || typeof ReactDOM === 'undefined') {
    console.error('React dependencies not loaded');
    return;
  }

  const { useState, useEffect, useCallback, useMemo, useRef } = React;

  // 应用配置
  const config = window.settings || {};

  // API 基础配置
  const API_BASE = config.api?.base_url || '/api/v1';
  const API_TIMEOUT = config.api?.timeout || 30000;

  // 路由配置
  const routes = {
    '/': 'dashboard',
    '/dashboard': 'dashboard',
    '/subscription': 'subscription',
    '/traffic': 'traffic',
    '/invite': 'invite',
    '/profile': 'profile',
    '/billing': 'billing',
    '/ticket': 'ticket',
    '/knowledge': 'knowledge'
  };

  // 主应用类
  class HeroApp {
    constructor() {
      this.container = document.getElementById('root');
      this.init();
    }

    init() {
      this.setupRouter();
      this.render();
      this.bindEvents();
      this.loadUserData();
    }

    // 设置路由
    setupRouter() {
      // 监听 hash 变化
      window.addEventListener('hashchange', () => {
        this.handleRouteChange();
      });

      // 初始路由
      this.handleRouteChange();
    }

    // 处理路由变化
    handleRouteChange() {
      const hash = window.location.hash.slice(1) || '/';
      appState.currentRoute = hash;
      this.render();
    }

    // 渲染应用
    render() {
      if (!this.container) return;

      const layout = this.createLayout();
      this.container.innerHTML = layout;
      
      // 重新绑定事件
      this.bindLayoutEvents();
    }

    // 创建布局
    createLayout() {
      return `
        <div class="hero-layout">
          ${this.createSidebar()}
          <div class="hero-main">
            ${this.createNavbar()}
            <div class="hero-content">
              ${this.createPageContent()}
            </div>
          </div>
        </div>
        ${appState.sidebarOpen ? '<div class="hero-overlay" onclick="app.closeSidebar()"></div>' : ''}
      `;
    }

    // 创建侧边栏
    createSidebar() {
      const collapsed = appState.sidebarCollapsed ? 'collapsed' : '';
      const open = appState.sidebarOpen ? 'open' : '';
      const style = appState.theme.sidebar_style || 'modern';
      
      return `
        <div class="hero-sidebar ${collapsed} ${open} ${style}">
          <div class="hero-sidebar-header">
            <div class="hero-logo">H</div>
            <div class="hero-brand">${config.title || 'HeroModern'}</div>
          </div>
          <nav class="hero-nav">
            ${this.createNavItems()}
          </nav>
        </div>
      `;
    }

    // 创建导航项
    createNavItems() {
      const navItems = [
        { path: '/dashboard', icon: '📊', label: '仪表板' },
        { path: '/subscription', icon: '📋', label: '订阅' },
        { path: '/traffic', icon: '📈', label: '流量' },
        { path: '/invite', icon: '👥', label: '邀请' },
        { path: '/profile', icon: '👤', label: '个人资料' },
        { path: '/billing', icon: '💳', label: '账单' },
        { path: '/ticket', icon: '🎫', label: '工单' },
        { path: '/knowledge', icon: '📚', label: '知识库' }
      ];

      return navItems.map(item => {
        const active = appState.currentRoute === item.path ? 'active' : '';
        return `
          <div class="hero-nav-item">
            <a href="#${item.path}" class="hero-nav-link ${active}">
              <span class="hero-nav-icon">${item.icon}</span>
              <span class="hero-nav-text">${item.label}</span>
            </a>
          </div>
        `;
      }).join('');
    }

    // 创建导航栏
    createNavbar() {
      const style = appState.theme.navbar_style || 'floating';
      const blur = appState.theme.blur ? 'hero-blur' : '';
      
      return `
        <div class="hero-navbar ${style} ${blur}">
          <div class="hero-navbar-left">
            <button class="hero-toggle-btn" onclick="app.toggleSidebar()">
              <span>☰</span>
            </button>
            <h1 style="margin: 0; font-size: 18px; font-weight: 600;">${this.getPageTitle()}</h1>
          </div>
          <div class="hero-navbar-right">
            <button class="heroui-button heroui-button-ghost heroui-button-small hero-theme-toggle" onclick="app.toggleTheme()">
              🌓
            </button>
            <div class="hero-user-menu" onclick="app.showUserMenu()">
              <div class="hero-avatar">U</div>
              <span>用户</span>
            </div>
          </div>
        </div>
      `;
    }

    // 获取页面标题
    getPageTitle() {
      const titles = {
        '/dashboard': '仪表板',
        '/subscription': '我的订阅',
        '/traffic': '流量统计',
        '/invite': '邀请好友',
        '/profile': '个人资料',
        '/billing': '账单管理',
        '/ticket': '工单系统',
        '/knowledge': '知识库'
      };
      return titles[appState.currentRoute] || '仪表板';
    }

    // 创建页面内容
    createPageContent() {
      const route = appState.currentRoute;
      
      switch (route) {
        case '/dashboard':
          return this.createDashboard();
        case '/subscription':
          return this.createSubscription();
        case '/traffic':
          return this.createTraffic();
        case '/invite':
          return this.createInvite();
        case '/profile':
          return this.createProfile();
        case '/billing':
          return this.createBilling();
        case '/ticket':
          return this.createTicket();
        case '/knowledge':
          return this.createKnowledge();
        default:
          return this.createDashboard();
      }
    }

    // 创建仪表板页面
    createDashboard() {
      return `
        <div class="hero-animate-fade">
          <div class="demo-grid" style="margin-bottom: 24px;">
            <div class="heroui-card">
              <div class="heroui-card-body">
                <h3 style="margin: 0 0 8px 0; color: var(--heroui-primary);">总用户数</h3>
                <div style="font-size: 32px; font-weight: 700; margin-bottom: 8px;">1,234</div>
                <p style="margin: 0; color: var(--heroui-content4); font-size: 14px;">+12% 较上月</p>
              </div>
            </div>
            
            <div class="heroui-card">
              <div class="heroui-card-body">
                <h3 style="margin: 0 0 8px 0; color: var(--heroui-success);">活跃订阅</h3>
                <div style="font-size: 32px; font-weight: 700; margin-bottom: 8px;">856</div>
                <p style="margin: 0; color: var(--heroui-content4); font-size: 14px;">+8% 较上月</p>
              </div>
            </div>
            
            <div class="heroui-card">
              <div class="heroui-card-body">
                <h3 style="margin: 0 0 8px 0; color: var(--heroui-warning);">总收入</h3>
                <div style="font-size: 32px; font-weight: 700; margin-bottom: 8px;">¥12,345</div>
                <p style="margin: 0; color: var(--heroui-content4); font-size: 14px;">+15% 较上月</p>
              </div>
            </div>
          </div>
          
          <div class="heroui-card">
            <div class="heroui-card-header">
              <h3>欢迎使用 HeroModern 主题</h3>
            </div>
            <div class="heroui-card-body">
              <p>这是一个基于 HeroUI 设计系统的现代化 v2board 主题。</p>
              <div style="margin-top: 16px;">
                <button class="heroui-button heroui-button-primary" onclick="hero.notify('欢迎使用！', 'success')">
                  测试通知
                </button>
                <button class="heroui-button heroui-button-secondary" onclick="app.navigate('/subscription')">
                  查看订阅
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
    }

    // 创建其他页面的占位内容
    createSubscription() {
      return `
        <div class="hero-animate-fade">
          <div class="heroui-card">
            <div class="heroui-card-header">
              <h3>我的订阅</h3>
            </div>
            <div class="heroui-card-body">
              <p>订阅管理功能正在开发中...</p>
            </div>
          </div>
        </div>
      `;
    }

    createTraffic() {
      return `
        <div class="hero-animate-fade">
          <div class="heroui-card">
            <div class="heroui-card-header">
              <h3>流量统计</h3>
            </div>
            <div class="heroui-card-body">
              <p>流量统计功能正在开发中...</p>
            </div>
          </div>
        </div>
      `;
    }

    createInvite() {
      return `
        <div class="hero-animate-fade">
          <div class="heroui-card">
            <div class="heroui-card-header">
              <h3>邀请好友</h3>
            </div>
            <div class="heroui-card-body">
              <p>邀请功能正在开发中...</p>
            </div>
          </div>
        </div>
      `;
    }

    createProfile() {
      return `
        <div class="hero-animate-fade">
          <div class="heroui-card">
            <div class="heroui-card-header">
              <h3>个人资料</h3>
            </div>
            <div class="heroui-card-body">
              <p>个人资料管理功能正在开发中...</p>
            </div>
          </div>
        </div>
      `;
    }

    createBilling() {
      return `
        <div class="hero-animate-fade">
          <div class="heroui-card">
            <div class="heroui-card-header">
              <h3>账单管理</h3>
            </div>
            <div class="heroui-card-body">
              <p>账单管理功能正在开发中...</p>
            </div>
          </div>
        </div>
      `;
    }

    createTicket() {
      return `
        <div class="hero-animate-fade">
          <div class="heroui-card">
            <div class="heroui-card-header">
              <h3>工单系统</h3>
            </div>
            <div class="heroui-card-body">
              <p>工单系统功能正在开发中...</p>
            </div>
          </div>
        </div>
      `;
    }

    createKnowledge() {
      return `
        <div class="hero-animate-fade">
          <div class="heroui-card">
            <div class="heroui-card-header">
              <h3>知识库</h3>
            </div>
            <div class="heroui-card-body">
              <p>知识库功能正在开发中...</p>
            </div>
          </div>
        </div>
      `;
    }

    // 绑定布局事件
    bindLayoutEvents() {
      // 导航链接点击事件
      document.querySelectorAll('.hero-nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const href = link.getAttribute('href');
          if (href) {
            window.location.hash = href;
          }
        });
      });
    }

    // 绑定全局事件
    bindEvents() {
      // 响应式处理
      window.addEventListener('resize', () => {
        if (window.innerWidth <= 768) {
          appState.sidebarCollapsed = false;
        }
        this.render();
      });
    }

    // 切换侧边栏
    toggleSidebar() {
      const isMobile = window.innerWidth <= 768;
      
      if (isMobile) {
        appState.sidebarOpen = !appState.sidebarOpen;
      } else {
        appState.sidebarCollapsed = !appState.sidebarCollapsed;
        localStorage.setItem('hero-sidebar-collapsed', appState.sidebarCollapsed);
      }
      
      this.render();
    }

    // 关闭侧边栏（移动端）
    closeSidebar() {
      appState.sidebarOpen = false;
      this.render();
    }

    // 切换主题
    toggleTheme() {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('hero-theme', newTheme);
      
      if (window.hero && window.hero.notify) {
        hero.notify(`已切换到${newTheme === 'dark' ? '暗色' : '亮色'}模式`, 'success');
      }
    }

    // 显示用户菜单
    showUserMenu() {
      if (window.hero && window.hero.notify) {
        hero.notify('用户菜单功能正在开发中', 'info');
      }
    }

    // 导航到指定路由
    navigate(path) {
      window.location.hash = path;
    }

    // 加载用户数据
    loadUserData() {
      // 模拟加载用户数据
      setTimeout(() => {
        appState.user = {
          name: '用户',
          email: '<EMAIL>',
          avatar: null
        };
      }, 1000);
    }
  }

  // 初始化应用
  document.addEventListener('DOMContentLoaded', () => {
    window.app = new HeroApp();
  });

})();
