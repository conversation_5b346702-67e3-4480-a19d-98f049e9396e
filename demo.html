<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HeroModern Theme Demo</title>
  <link rel="stylesheet" href="assets/heroui.min.css">
  <link rel="stylesheet" href="assets/modern-theme.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: var(--heroui-background);
      color: var(--heroui-foreground);
    }
    
    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }
    
    .demo-header {
      text-align: center;
      margin-bottom: 60px;
    }
    
    .demo-title {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 16px;
      background: linear-gradient(135deg, var(--heroui-primary), var(--heroui-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .demo-subtitle {
      font-size: 20px;
      color: var(--heroui-content4);
      margin-bottom: 32px;
    }
    
    .demo-section {
      margin-bottom: 60px;
    }
    
    .demo-section-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 24px;
      color: var(--heroui-foreground);
    }
    
    .demo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
      margin-bottom: 32px;
    }
    
    .demo-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 24px;
    }
    
    .demo-components {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;
    }
    
    .theme-toggle {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }
    
    .color-picker {
      display: flex;
      gap: 8px;
      margin-top: 16px;
    }
    
    .color-option {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      border: 2px solid var(--heroui-divider);
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .color-option:hover {
      transform: scale(1.1);
      box-shadow: var(--heroui-shadow-medium);
    }
    
    .color-option.active {
      border-color: var(--heroui-foreground);
      transform: scale(1.1);
    }
  </style>
</head>
<body>
  <!-- 主题切换按钮 -->
  <button class="heroui-button heroui-button-primary theme-toggle" onclick="toggleTheme()">
    🌓 切换主题
  </button>

  <div class="demo-container">
    <!-- 头部 -->
    <div class="demo-header">
      <h1 class="demo-title">HeroModern</h1>
      <p class="demo-subtitle">基于 HeroUI 的现代化 v2board 主题</p>
      <div class="demo-buttons">
        <a href="dashboard.blade.php" class="heroui-button heroui-button-primary">查看完整主题</a>
        <button class="heroui-button heroui-button-secondary">查看文档</button>
        <button class="heroui-button heroui-button-ghost">GitHub</button>
      </div>
      
      <!-- 主题色选择器 -->
      <div class="color-picker">
        <div class="color-option" style="background: #006FEE" data-color="blue"></div>
        <div class="color-option" style="background: #7C3AED" data-color="purple"></div>
        <div class="color-option" style="background: #17C964" data-color="green"></div>
        <div class="color-option" style="background: #F5A524" data-color="orange"></div>
        <div class="color-option" style="background: #F31260" data-color="pink"></div>
        <div class="color-option" style="background: #64748B" data-color="slate"></div>
      </div>
    </div>

    <!-- 按钮组件 -->
    <div class="demo-section">
      <h2 class="demo-section-title">按钮组件</h2>
      <div class="demo-buttons">
        <button class="heroui-button heroui-button-primary">主要按钮</button>
        <button class="heroui-button heroui-button-secondary">次要按钮</button>
        <button class="heroui-button heroui-button-success">成功按钮</button>
        <button class="heroui-button heroui-button-warning">警告按钮</button>
        <button class="heroui-button heroui-button-danger">危险按钮</button>
        <button class="heroui-button heroui-button-ghost">幽灵按钮</button>
      </div>
      <div class="demo-buttons">
        <button class="heroui-button heroui-button-primary heroui-button-small">小按钮</button>
        <button class="heroui-button heroui-button-primary">默认按钮</button>
        <button class="heroui-button heroui-button-primary heroui-button-large">大按钮</button>
      </div>
    </div>

    <!-- 卡片组件 -->
    <div class="demo-section">
      <h2 class="demo-section-title">卡片组件</h2>
      <div class="demo-grid">
        <div class="heroui-card">
          <div class="heroui-card-header">
            <h3>基础卡片</h3>
          </div>
          <div class="heroui-card-body">
            <p>这是一个基础的卡片组件，具有简洁的设计和优雅的阴影效果。</p>
          </div>
        </div>
        
        <div class="heroui-card">
          <div class="heroui-card-header">
            <h3>统计卡片</h3>
          </div>
          <div class="heroui-card-body">
            <div style="font-size: 32px; font-weight: 700; color: var(--heroui-primary); margin-bottom: 8px;">1,234</div>
            <p style="margin: 0; color: var(--heroui-content4);">总用户数</p>
          </div>
        </div>
        
        <div class="heroui-card">
          <div class="heroui-card-header">
            <h3>功能卡片</h3>
          </div>
          <div class="heroui-card-body">
            <p>支持头部、主体和底部的完整布局结构。</p>
          </div>
          <div class="heroui-card-footer">
            <button class="heroui-button heroui-button-primary heroui-button-small">操作</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 表单组件 -->
    <div class="demo-section">
      <h2 class="demo-section-title">表单组件</h2>
      <div class="heroui-card" style="max-width: 500px;">
        <div class="heroui-card-body">
          <div style="margin-bottom: 16px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500;">用户名</label>
            <input type="text" class="heroui-input" placeholder="请输入用户名">
          </div>
          <div style="margin-bottom: 16px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500;">邮箱</label>
            <input type="email" class="heroui-input" placeholder="请输入邮箱地址">
          </div>
          <div style="margin-bottom: 24px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500;">密码</label>
            <input type="password" class="heroui-input" placeholder="请输入密码">
          </div>
          <button class="heroui-button heroui-button-primary" style="width: 100%;">提交</button>
        </div>
      </div>
    </div>

    <!-- 其他组件 -->
    <div class="demo-section">
      <h2 class="demo-section-title">其他组件</h2>
      <div class="demo-components">
        <!-- 头像 -->
        <div class="heroui-avatar heroui-avatar-small">A</div>
        <div class="heroui-avatar heroui-avatar-medium">B</div>
        <div class="heroui-avatar heroui-avatar-large">C</div>
        
        <!-- 徽章 -->
        <div class="heroui-badge heroui-badge-primary">Primary</div>
        <div class="heroui-badge heroui-badge-success">Success</div>
        <div class="heroui-badge heroui-badge-warning">Warning</div>
        <div class="heroui-badge heroui-badge-danger">Danger</div>
        
        <!-- 芯片 -->
        <div class="heroui-chip">
          标签
          <button class="heroui-chip-close">×</button>
        </div>
        
        <!-- 加载器 -->
        <div class="heroui-spinner"></div>
        <div class="heroui-spinner heroui-spinner-large"></div>
      </div>
      
      <!-- 进度条 -->
      <div style="margin-top: 24px;">
        <div class="heroui-progress">
          <div class="heroui-progress-bar" style="width: 60%;"></div>
        </div>
      </div>
    </div>
  </div>

  <script src="assets/heroui.min.js"></script>
  <script>
    // 主题切换
    function toggleTheme() {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('demo-theme', newTheme);
    }

    // 颜色切换
    document.querySelectorAll('.color-option').forEach(option => {
      option.addEventListener('click', () => {
        const color = option.dataset.color;
        const colors = {
          blue: '#006FEE',
          purple: '#7C3AED',
          green: '#17C964',
          orange: '#F5A524',
          pink: '#F31260',
          slate: '#64748B'
        };
        
        document.documentElement.style.setProperty('--heroui-primary', colors[color]);
        
        // 更新活跃状态
        document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('active'));
        option.classList.add('active');
        
        localStorage.setItem('demo-color', color);
      });
    });

    // 初始化主题
    const savedTheme = localStorage.getItem('demo-theme');
    const savedColor = localStorage.getItem('demo-color');
    
    if (savedTheme) {
      document.documentElement.setAttribute('data-theme', savedTheme);
    } else {
      // 检测系统主题
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    }
    
    if (savedColor) {
      const colorOption = document.querySelector(`[data-color="${savedColor}"]`);
      if (colorOption) {
        colorOption.click();
      }
    } else {
      document.querySelector('[data-color="blue"]').classList.add('active');
    }

    // 演示通知
    setTimeout(() => {
      if (window.heroToast) {
        heroToast('欢迎使用 HeroModern 主题！', 'success');
      }
    }, 1000);
  </script>
</body>
</html>
