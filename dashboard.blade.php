<!DOCTYPE html>
<html>

<head>
  <!-- HeroModern Theme CSS -->
  <link rel="stylesheet" href="/theme/{{$theme}}/assets/components.chunk.css?v={{$version}}">
  <link rel="stylesheet" href="/theme/{{$theme}}/assets/umi.css?v={{$version}}">
  @if (file_exists(public_path("/theme/{$theme}/assets/custom.css")))
    <link rel="stylesheet" href="/theme/{{$theme}}/assets/custom.css?v={{$version}}">
  @endif
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
  
  @php ($colors = [
    'blue' => '#006FEE',
    'purple' => '#7C3AED', 
    'green' => '#17C964',
    'orange' => '#F5A524',
    'pink' => '#F31260',
    'slate' => '#64748B'
  ])
  <meta name="theme-color" content="{{$colors[$theme_config['theme_color'] ?? 'blue']}}">

  <title>{{$title}}</title>
  <script>window.routerBase = "/";</script>
  <script>
    window.settings = {
      title: '{{$title}}',
      assets_path: '/theme/{{$theme}}/assets',
      theme: {
        mode: '{{$theme_config['theme_mode'] ?? 'auto'}}',
        color: '{{$theme_config['theme_color'] ?? 'blue'}}',
        sidebar: '{{$theme_config['theme_sidebar'] ?? 'modern'}}',
        header: '{{$theme_config['theme_header'] ?? 'floating'}}',
        animations: {{$theme_config['enable_animations'] ?? 'true'}},
        blur: {{$theme_config['enable_blur'] ?? 'true'}},
        border_radius: '{{$theme_config['border_radius'] ?? 'medium'}}'
      },
      version: '{{$version}}',
      background_url: '{{$theme_config['background_url']}}',
      description: '{{$description}}',
      i18n: [
        'zh-CN',
        'zh-TW',
        'en-US', 
        'ja-JP',
        'ko-KR'
      ],
      logo: '{{$logo}}'
    }
  </script>
</head>

<body>
  <div id="root">
    <!-- 加载指示器 -->
    <div style="
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background: #ffffff;
      color: #11181c;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    ">
      <div style="text-align: center;">
        <div style="
          width: 40px;
          height: 40px;
          border: 4px solid #e4e4e7;
          border-top: 4px solid #006fee;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px auto;
        "></div>
        <p style="margin: 0; color: #71717a;">正在加载 HeroModern...</p>
      </div>
    </div>
    
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </div>
  
  {!! $theme_config['custom_html'] ?? '' !!}
  
  <!-- React 依赖 - 使用开发版本便于调试 -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  
  <!-- 国际化文件 -->
  <script src="/theme/{{$theme}}/assets/i18n/zh-CN.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/zh-TW.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/en-US.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/ja-JP.js?v={{$version}}"></script>
  <script src="/theme/{{$theme}}/assets/i18n/ko-KR.js?v={{$version}}"></script>
  
  <!-- 等待React加载完成 -->
  <script>
    function waitForReact(callback) {
      if (typeof React !== 'undefined' && typeof ReactDOM !== 'undefined') {
        console.log('✅ React loaded successfully');
        callback();
      } else {
        console.log('⏳ Waiting for React...');
        setTimeout(() => waitForReact(callback), 100);
      }
    }
    
    // 加载应用脚本
    function loadAppScripts() {
      console.log('📦 Loading application scripts...');
      
      const scripts = [
        '/theme/{{$theme}}/assets/vendors.async.js?v={{$version}}',
        '/theme/{{$theme}}/assets/components.async.js?v={{$version}}',
        '/theme/{{$theme}}/assets/hero-api.js?v={{$version}}',
        '/theme/{{$theme}}/assets/hero-auth.js?v={{$version}}',
        '/theme/{{$theme}}/assets/hero-pages.js?v={{$version}}',
        '/theme/{{$theme}}/assets/umi.js?v={{$version}}'
      ];
      
      let loadedCount = 0;
      
      scripts.forEach((src, index) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => {
          loadedCount++;
          console.log(`✅ Loaded: ${src}`);
          if (loadedCount === scripts.length) {
            console.log('🎉 All scripts loaded successfully');
          }
        };
        script.onerror = () => {
          console.error(`❌ Failed to load: ${src}`);
        };
        document.head.appendChild(script);
      });
    }
    
    // 等待React加载后开始加载应用脚本
    waitForReact(loadAppScripts);
  </script>
  
  @if (file_exists(public_path("/theme/{$theme}/assets/custom.js")))
    <script src="/theme/{{$theme}}/assets/custom.js?v={{$version}}"></script>
  @endif
</body>

</html>
