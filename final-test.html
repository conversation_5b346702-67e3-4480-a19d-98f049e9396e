<!DOCTYPE html>
<html>

<head>
  <!-- HeroModern Theme CSS -->
  <link rel="stylesheet" href="assets/components.chunk.css">
  <link rel="stylesheet" href="assets/umi.css">
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
  <meta name="theme-color" content="#006FEE">

  <title>HeroModern Final Test</title>
  <script>window.routerBase = "/";</script>
  <script>
    window.settings = {
      title: 'HeroModern Final Test',
      assets_path: 'assets',
      theme: {
        mode: 'auto',
        color: 'blue',
        sidebar: 'modern',
        header: 'floating',
        animations: true,
        blur: true,
        border_radius: 'medium'
      },
      version: '1.0.0',
      background_url: '',
      description: 'HeroModern 最终测试',
      i18n: [
        'zh-CN',
        'zh-TW',
        'en-US', 
        'ja-<PERSON>',
        'ko-KR'
      ],
      logo: ''
    }
  </script>
</head>

<body>
  <div id="root">
    <!-- 加载指示器 -->
    <div style="
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background: #ffffff;
      color: #11181c;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    ">
      <div style="text-align: center;">
        <div style="
          width: 40px;
          height: 40px;
          border: 4px solid #e4e4e7;
          border-top: 4px solid #006fee;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px auto;
        "></div>
        <p style="margin: 0; color: #71717a;">正在加载 HeroModern...</p>
      </div>
    </div>
    
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </div>
  
  <!-- React 依赖 - 使用开发版本便于调试 -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  
  <!-- 国际化文件 -->
  <script src="assets/i18n/zh-CN.js"></script>
  <script src="assets/i18n/zh-TW.js"></script>
  <script src="assets/i18n/en-US.js"></script>
  <script src="assets/i18n/ja-JP.js"></script>
  <script src="assets/i18n/ko-KR.js"></script>
  
  <!-- 等待React加载完成 -->
  <script>
    function waitForReact(callback) {
      if (typeof React !== 'undefined' && typeof ReactDOM !== 'undefined') {
        console.log('✅ React loaded successfully');
        console.log('React version:', React.version);
        callback();
      } else {
        console.log('⏳ Waiting for React...');
        setTimeout(() => waitForReact(callback), 100);
      }
    }
    
    // 加载应用脚本
    function loadAppScripts() {
      console.log('📦 Loading application scripts...');
      
      const scripts = [
        'assets/vendors.async.js',
        'assets/components.async.js',
        'assets/hero-api.js',
        'assets/hero-auth.js',
        'assets/hero-pages.js',
        'assets/umi.js'
      ];
      
      let loadedCount = 0;
      
      scripts.forEach((src, index) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => {
          loadedCount++;
          console.log(`✅ Loaded (${loadedCount}/${scripts.length}): ${src}`);
          if (loadedCount === scripts.length) {
            console.log('🎉 All scripts loaded successfully');
          }
        };
        script.onerror = () => {
          console.error(`❌ Failed to load: ${src}`);
        };
        document.head.appendChild(script);
      });
    }
    
    // 全局错误处理
    window.addEventListener('error', (event) => {
      console.error('❌ Global error:', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
      console.error('❌ Unhandled promise rejection:', event.reason);
    });
    
    // 等待React加载后开始加载应用脚本
    console.log('🚀 Starting HeroModern Final Test...');
    waitForReact(loadAppScripts);
  </script>
</body>

</html>
