/* HeroModern Theme - Components CSS */

/* 基础变量 */
:root {
  /* HeroUI 主题变量 */
  --heroui-primary: #006FEE;
  --heroui-secondary: #9353D3;
  --heroui-success: #17C964;
  --heroui-warning: #F5A524;
  --heroui-danger: #F31260;
  --heroui-foreground: #11181C;
  --heroui-background: #FFFFFF;
  --heroui-content1: #FFFFFF;
  --heroui-content2: #F4F4F5;
  --heroui-content3: #E4E4E7;
  --heroui-content4: #D4D4D8;
  --heroui-default: #D4D4D8;
  --heroui-divider: #E4E4E7;
  --heroui-focus: #006FEE;
  --heroui-overlay: rgba(0, 0, 0, 0.5);
  --heroui-radius-small: 8px;
  --heroui-radius-medium: 12px;
  --heroui-radius-large: 16px;
}

/* 暗色模式变量 */
[data-theme="dark"] {
  --heroui-foreground: #ECEDEE;
  --heroui-background: #000000;
  --heroui-content1: #18181B;
  --heroui-content2: #27272A;
  --heroui-content3: #3F3F46;
  --heroui-content4: #52525B;
  --heroui-default: #3F3F46;
  --heroui-divider: #27272A;
  --heroui-overlay: rgba(0, 0, 0, 0.8);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background: var(--heroui-background);
  color: var(--heroui-foreground);
  transition: background-color 0.3s ease, color 0.3s ease;
  line-height: 1.6;
}

/* 背景渐变 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--heroui-primary) 0%, var(--heroui-secondary) 100%);
  opacity: 0.05;
  z-index: -2;
}

/* 主应用容器 */
#root {
  position: relative;
  z-index: 1;
  min-height: 100vh;
}

/* 加载动画 */
.hero-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: var(--heroui-background);
}

.hero-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--heroui-content3);
  border-top: 4px solid var(--heroui-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* HeroUI 组件样式 */
.heroui-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: var(--heroui-radius-medium);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-height: 40px;
}

.heroui-button-primary {
  background: var(--heroui-primary);
  color: white;
}

.heroui-button-primary:hover {
  opacity: 0.8;
}

.heroui-button-secondary {
  background: var(--heroui-secondary);
  color: white;
}

.heroui-button-secondary:hover {
  opacity: 0.8;
}

.heroui-button-success {
  background: var(--heroui-success);
  color: white;
}

.heroui-button-warning {
  background: var(--heroui-warning);
  color: white;
}

.heroui-button-danger {
  background: var(--heroui-danger);
  color: white;
}

.heroui-button-ghost {
  background: transparent;
  color: var(--heroui-foreground);
  border: 1px solid var(--heroui-divider);
}

.heroui-button-ghost:hover {
  background: var(--heroui-content2);
}

.heroui-button-small {
  padding: 4px 12px;
  font-size: 12px;
  min-height: 32px;
}

.heroui-card {
  background: var(--heroui-content1);
  border: 1px solid var(--heroui-divider);
  border-radius: var(--heroui-radius-large);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.heroui-card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--heroui-divider);
}

.heroui-card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.heroui-card-body {
  padding: 20px;
}

.heroui-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--heroui-divider);
  border-radius: var(--heroui-radius-medium);
  background: var(--heroui-background);
  color: var(--heroui-foreground);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.heroui-input:focus {
  outline: none;
  border-color: var(--heroui-primary);
}

.heroui-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: var(--heroui-radius-small);
  font-size: 12px;
  font-weight: 500;
}

.heroui-badge-primary {
  background: var(--heroui-primary);
  color: white;
}

.heroui-badge-secondary {
  background: var(--heroui-content3);
  color: var(--heroui-foreground);
}

.heroui-badge-success {
  background: var(--heroui-success);
  color: white;
}

.heroui-badge-warning {
  background: var(--heroui-warning);
  color: white;
}

.heroui-badge-danger {
  background: var(--heroui-danger);
  color: white;
}

.heroui-progress {
  width: 100%;
  height: 8px;
  background: var(--heroui-content3);
  border-radius: var(--heroui-radius-small);
  overflow: hidden;
}

.heroui-progress-bar {
  height: 100%;
  background: var(--heroui-primary);
  transition: width 0.3s ease;
}

/* 动画效果 */
.hero-animate-fade {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .heroui-card-body {
    padding: 16px;
  }
  
  .heroui-card-header {
    padding: 12px 16px;
  }
}

/* 主题色彩变量 - 通过JavaScript动态设置 */
.theme-blue {
  --heroui-primary: #006FEE;
  --heroui-secondary: #338EF7;
}

.theme-purple {
  --heroui-primary: #7C3AED;
  --heroui-secondary: #9353D3;
}

.theme-green {
  --heroui-primary: #17C964;
  --heroui-secondary: #45D483;
}

.theme-orange {
  --heroui-primary: #F5A524;
  --heroui-secondary: #F7B955;
}

.theme-pink {
  --heroui-primary: #F31260;
  --heroui-secondary: #F54180;
}

.theme-slate {
  --heroui-primary: #64748B;
  --heroui-secondary: #94A3B8;
}
